server:
  port: 8091
  servlet:
    context-path: /ims



api:
  url: http://10.142.57.135:8086/ims
  clientId: 10
  line: 400
  nodeType : slaver
spring:
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: **********************************************************************************************************************************************************
      username: root
      password: indoorSystem#123
      initialSize: 5
      minIdle: 10
      maxActive: 20
      maxWait: 86400
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username: admin
        login-password: 123456
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 3000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true