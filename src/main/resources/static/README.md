# 设备管理系统前端页面

## 概述

本项目为厦门室分监测系统提供了完整的前端页面实现，主要用于管理 `DeviceDbApi` 类提供的设备管理功能。

## 功能特性

### 1. 设备列表管理
- **实时查询**: 调用 `GET /db/device/query` 接口获取设备列表
- **设备展示**: 以表格形式展示设备的详细信息
- **状态监控**: 实时显示设备在线状态（在线/离线/待机/关机/未知）
- **统计信息**: 显示总设备数、在线设备数、离线设备数

### 2. 设备信息管理
- **添加设备**: 通过表单添加新设备
- **编辑设备**: 修改现有设备信息
- **删除设备**: 删除不需要的设备
- **数据验证**: 前端表单验证确保数据完整性

### 3. 告警查询功能
✅ **告警记录查询**
- 调用 `GET /alarm/query` 接口
- 支持按时间范围、告警状态等条件筛选
- 分页显示告警记录
- 实时统计告警数量（产生/恢复/确认）

✅ **告警信息展示**
- 表格形式展示告警详细信息
- 告警状态可视化（产生/恢复/确认）
- 告警编码分类显示（紧急/重要/一般）
- 站点名称自动映射显示
- 支持分页浏览
- 响应式设计适配各种设备

### 4. 用户界面
- **响应式设计**: 支持桌面和移动设备
- **Bootstrap 5**: 现代化的UI组件
- **实时反馈**: 操作结果的即时提示
- **加载状态**: 数据加载时的友好提示

## 页面结构

```
src/main/resources/static/
├── index.html              # 系统主页
├── device-management.html  # 设备管理页面
├── alarm-query.html        # 告警查询页面（支持站点名称映射）
├── test-api.html          # 设备API测试页面
├── test-alarm-api.html    # 告警API测试页面
├── test-station-api.html  # 站点API测试页面
└── README.md              # 说明文档
```

## API 接口对应

### 1. 设备查询接口
- **前端调用**: `loadDeviceList()` 函数
- **后端接口**: `GET /db/device/query`
- **功能**: 获取所有设备列表数据

### 2. 设备变更接口
- **前端调用**: `saveDevice()` 和 `deleteDevice()` 函数
- **后端接口**: `POST /db/device/info/changeReport`
- **功能**: 设备信息的增加、修改、删除操作

### 3. 告警查询接口
- **前端调用**: `queryAlarms()` 函数
- **后端接口**: `GET /alarm/query`
- **功能**: 查询告警记录数据，支持分页和条件筛选

### 4. 站点映射接口
- **前端调用**: `loadStationMappings()` 和 `getStationDisplayName()` 函数
- **后端接口**: `GET /station/mapping` 和 `GET /station/name`
- **功能**: 获取站点映射数据，根据线路号和站点ID显示站点名称

## 使用说明

### 1. 启动应用
确保Spring Boot应用正常启动，然后访问：
- **主页**: `http://localhost:8091/ims/`
- **设备管理**: `http://localhost:8091/ims/device-management.html`
- **告警查询**: `http://localhost:8091/ims/alarm-query.html`
- **设备API测试**: `http://localhost:8091/ims/test-api.html`
- **告警API测试**: `http://localhost:8091/ims/test-alarm-api.html`
- **站点API测试**: `http://localhost:8091/ims/test-station-api.html`

> 注意：您的应用运行在端口8091，上下文路径为`/ims`

### 2. 设备管理操作

#### 查看设备列表
1. 打开设备管理页面
2. 页面自动加载设备列表
3. 可点击"刷新设备列表"按钮手动刷新

#### 添加设备
1. 点击"添加设备"按钮
2. 填写设备信息表单
3. 必填字段：设备SN、设备名称、设备类别、IP地址
4. 点击"保存"按钮提交

#### 编辑设备
1. 在设备列表中点击"编辑"按钮
2. 修改设备信息
3. 点击"保存"按钮提交更改

#### 删除设备
1. 在设备列表中点击"删除"按钮
2. 确认删除操作
3. 系统将调用后端API删除设备

### 3. 告警查询操作

#### 查看告警列表
1. 打开告警查询页面
2. 页面自动加载最近7天的告警数据
3. 可查看告警统计信息（产生/恢复/确认数量）

#### 条件查询
1. 设置查询条件：
   - **时间范围**: 选择开始时间和结束时间
   - **告警状态**: 选择特定状态或查看全部
   - **告警级别**: 选择紧急/重要/一般告警或查看全部
   - **每页条数**: 设置分页大小
2. 点击"查询"按钮执行查询
3. 点击"重置"按钮清空条件并恢复默认设置

#### 分页浏览
1. 使用页面底部的分页控件
2. 支持首页、末页、上一页、下一页导航
3. 显示当前页码和总记录数

### 3. 站点映射功能

#### 站点名称显示
- 告警查询页面会自动加载站点映射数据
- 在告警列表中，站点信息列显示"站点名称 (站点ID)"格式
- 支持4号线和6号线的站点名称映射

#### 站点API测试
1. 访问站点API测试页面
2. 测试站点映射接口获取所有站点数据
3. 测试站点名称接口根据线路ID和站点ID获取名称
4. 查看站点映射数据的可视化展示

#### 线路和站点对应关系
**4号线 (400)**: 软三东站、后溪站、厦门北站、官浔站、丙洲岛站、城场站、双过村站、洪坑站、彭厝北站、蔡厝站、机场西站、翔安机场站

**6号线 (600)**: 龙江明珠站、石美站、角江路站、龟山站、文圃路站、角海路站、社头站、林埭西站、林埭站、鼎美站、马銮中心站、集美岛站、西滨路口站、杏林西路站、杏滨站、内茂站、董任站、官任站、集美创业园站、华侨大学站

### 4. 告警编码分类说明

| 编码值 | 告警级别 | 显示样式 | 颜色标识 |
|--------|----------|----------|----------|
| 1      | 紧急告警 | 红色徽章 | 危险红色 |
| 2      | 重要告警 | 黄色徽章 | 警告黄色 |
| 3      | 一般告警 | 蓝色徽章 | 信息蓝色 |
| 其他   | 原始编码 | 灰色徽章 | 默认灰色 |

### 5. 告警状态说明

| 状态值 | 显示文本 | 颜色标识 |
|--------|----------|----------|
| 1      | 告警产生 | 红色     |
| 2      | 告警恢复 | 绿色     |
| 3      | 告警确认 | 灰色     |

### 6. 设备状态说明

| 状态值 | 显示文本 | 颜色标识 |
|--------|----------|----------|
| 1      | 在线     | 绿色     |
| 2      | 离线     | 红色     |
| 3      | 待机     | 黄色     |
| 4      | 关机     | 灰色     |
| 5      | 未知     | 蓝色     |

### 4. 工作模式说明

| 模式值 | 显示文本 |
|--------|----------|
| 1      | 主用     |
| 2      | 备用     |

### 5. 告警状态说明

| 状态值 | 显示文本 | 颜色标识 | 说明 |
|--------|----------|----------|------|
| 1      | 告警产生 | 红色     | 告警产生，未处理状态 |
| 2      | 告警恢复 | 绿色     | 告警已恢复 |
| 3      | 告警确认 | 灰色     | 告警已确认 |

## 技术栈

- **HTML5**: 页面结构
- **CSS3**: 样式设计
- **JavaScript (ES6+)**: 交互逻辑
- **Bootstrap 5**: UI框架
- **Bootstrap Icons**: 图标库
- **Fetch API**: HTTP请求

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. **网络连接**: 确保前端页面能够访问后端API接口
2. **CORS设置**: 如果前后端分离部署，需要配置CORS
3. **数据格式**: 前端发送的数据格式需要与后端API期望的格式一致
4. **错误处理**: 页面包含完整的错误处理和用户提示
5. **数据验证**: 前端进行基础验证，后端也应该有相应的验证逻辑

## 自定义配置

### 修改API基础路径
如果需要修改API路径，请在JavaScript代码中更新相应的URL：

```javascript
// 设备查询接口
const response = await fetch('/db/device/query');

// 设备变更接口
const response = await fetch('/db/device/info/changeReport', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(deviceData)
});
```

### 添加新的设备类别
在设备管理页面的类别下拉框中添加新选项：

```html
<select class="form-select" id="category" name="category" required>
    <option value="">请选择类别</option>
    <option value="监控主机">监控主机</option>
    <option value="室分天线">室分天线</option>
    <option value="其他设备">其他设备</option>
    <!-- 在这里添加新的类别选项 -->
</select>
```

## 故障排除

### 1. 页面无法加载设备数据
- 检查后端服务是否正常运行
- 检查API接口路径是否正确
- 查看浏览器控制台的错误信息

### 2. 设备操作失败
- 检查网络连接
- 验证提交的数据格式
- 查看后端日志确认错误原因

### 3. 页面样式异常
- 检查Bootstrap CSS是否正确加载
- 确认网络连接正常
- 清除浏览器缓存

## 扩展功能建议

1. **搜索过滤**: 添加设备名称、类别等搜索功能
2. **批量操作**: 支持批量删除、批量修改设备状态
3. **导出功能**: 支持设备列表导出为Excel或CSV
4. **实时更新**: 使用WebSocket实现设备状态的实时更新
5. **权限控制**: 添加用户权限管理功能
