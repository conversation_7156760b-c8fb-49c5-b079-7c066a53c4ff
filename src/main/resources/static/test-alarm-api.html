<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>告警API测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">告警API测试</h1>
        
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>GET /alarm/query</h5>
                        <small class="text-muted">查询告警记录数据</small>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">开始时间:</label>
                                    <input type="datetime-local" class="form-control" id="beginTime" name="beginTime">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">结束时间:</label>
                                    <input type="datetime-local" class="form-control" id="endTime" name="endTime">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">告警状态:</label>
                                    <select class="form-select" id="alarmStatus" name="alarmStatus">
                                        <option value="">全部</option>
                                        <option value="1">告警产生</option>
                                        <option value="2">告警恢复</option>
                                        <option value="3">告警确认</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">页码:</label>
                                    <input type="number" class="form-control" id="pageNo" name="pageNo" value="1" min="1">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">每页条数:</label>
                                    <input type="number" class="form-control" id="pageSize" name="pageSize" value="20" min="1" max="100">
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <button type="button" class="btn btn-primary" onclick="testAlarmQuery()">测试查询接口</button>
                                    <button type="button" class="btn btn-secondary" onclick="resetTestForm()">重置</button>
                                </div>
                            </div>
                        </form>
                        
                        <div class="mt-4">
                            <label class="form-label">请求URL:</label>
                            <div id="requestUrl" class="code-block bg-light">点击测试按钮查看请求URL...</div>
                        </div>
                        
                        <div class="mt-3">
                            <label class="form-label">响应结果:</label>
                            <div id="queryResult" class="code-block">点击按钮测试接口...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>接口说明</h5>
            </div>
            <div class="card-body">
                <h6>1. 告警查询接口</h6>
                <ul>
                    <li><strong>URL:</strong> GET /alarm/query</li>
                    <li><strong>功能:</strong> 查询告警记录数据，支持分页和条件筛选</li>
                    <li><strong>参数:</strong>
                        <ul>
                            <li><code>beginTime</code> - 开始时间（可选）</li>
                            <li><code>endTime</code> - 结束时间（可选）</li>
                            <li><code>alarmIds</code> - 告警ID列表（可选）</li>
                            <li><code>alarmStatus</code> - 告警状态（可选）：1=告警产生，2=告警恢复，3=告警确认</li>
                            <li><code>pageNo</code> - 页码（可选，默认1）</li>
                            <li><code>pageSize</code> - 每页条数（可选，默认20）</li>
                        </ul>
                    </li>
                    <li><strong>返回:</strong> JsonResult格式，包含分页的告警数据</li>
                </ul>
                
                <h6>2. 告警状态说明</h6>
                <ul>
                    <li><code>1</code> - 告警产生，未处理状态</li>
                    <li><code>2</code> - 告警恢复</li>
                    <li><code>3</code> - 告警确认</li>
                </ul>
                
                <h6>3. 响应格式</h6>
                <pre class="code-block">{
  "retCode": 0,           // 0表示成功，其他表示失败
  "retMessage": "ok",     // 响应消息
  "result": {
    "records": [...],     // 告警记录数组
    "total": 100,         // 总记录数
    "size": 20,           // 每页大小
    "current": 1,         // 当前页码
    "pages": 5            // 总页数
  }
}</pre>

                <h6>4. 告警记录字段说明</h6>
                <ul>
                    <li><code>alarmId</code> - 告警ID</li>
                    <li><code>deviceId</code> - 设备ID</li>
                    <li><code>stationId</code> - 站点ID</li>
                    <li><code>code</code> - 告警编码</li>
                    <li><code>status</code> - 告警状态</li>
                    <li><code>alarmTime</code> - 告警发生时间</li>
                    <li><code>recoveryTime</code> - 告警恢复时间</li>
                    <li><code>line</code> - 线路</li>
                    <li><code>system</code> - 系统</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认时间范围（最近24小时）
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            
            document.getElementById('beginTime').value = formatDateTimeLocal(yesterday);
            document.getElementById('endTime').value = formatDateTimeLocal(now);
        });

        // 测试告警查询接口
        async function testAlarmQuery() {
            const resultDiv = document.getElementById('queryResult');
            const urlDiv = document.getElementById('requestUrl');
            
            resultDiv.textContent = '正在请求...';
            resultDiv.style.color = '#000';
            
            try {
                const formData = new FormData(document.getElementById('testForm'));
                const params = new URLSearchParams();
                
                // 添加查询参数
                if (formData.get('beginTime')) {
                    params.append('beginTime', formatDateTime(formData.get('beginTime')));
                }
                if (formData.get('endTime')) {
                    params.append('endTime', formatDateTime(formData.get('endTime')));
                }
                if (formData.get('alarmStatus')) {
                    params.append('alarmStatus', formData.get('alarmStatus'));
                }
                if (formData.get('pageNo')) {
                    params.append('pageNo', formData.get('pageNo'));
                }
                if (formData.get('pageSize')) {
                    params.append('pageSize', formData.get('pageSize'));
                }
                
                const url = `/alarm/query?${params.toString()}`;
                urlDiv.textContent = url;
                
                const response = await fetch(url);
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (result.retCode === 0) {
                    resultDiv.style.color = '#28a745';
                } else {
                    resultDiv.style.color = '#dc3545';
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
                resultDiv.style.color = '#dc3545';
                urlDiv.textContent = '请求失败';
            }
        }
        
        // 重置测试表单
        function resetTestForm() {
            document.getElementById('testForm').reset();
            
            // 重新设置默认时间
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            
            document.getElementById('beginTime').value = formatDateTimeLocal(yesterday);
            document.getElementById('endTime').value = formatDateTimeLocal(now);
            document.getElementById('pageNo').value = '1';
            document.getElementById('pageSize').value = '20';
            
            document.getElementById('queryResult').textContent = '点击按钮测试接口...';
            document.getElementById('queryResult').style.color = '#000';
            document.getElementById('requestUrl').textContent = '点击测试按钮查看请求URL...';
        }

        // 格式化日期时间为本地输入格式
        function formatDateTimeLocal(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            
            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        // 格式化日期时间为API格式
        function formatDateTime(dateTimeLocal) {
            if (!dateTimeLocal) return '';
            const date = new Date(dateTimeLocal);
            return date.toISOString().replace('T', ' ').substring(0, 23);
        }
    </script>
</body>
</html>
