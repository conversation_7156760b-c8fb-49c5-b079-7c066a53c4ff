<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>告警查询系统</title>
    <link href="js/bootstrap.min.css" rel="stylesheet">
    <link href="bootstrap-icons-1.13.1/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-active { color: #dc3545; }
        .status-recovered { color: #28a745; }
        .status-confirmed { color: #6c757d; }
        .level-1 { color: #dc3545; font-weight: bold; }
        .level-2 { color: #fd7e14; font-weight: bold; }
        .level-3 { color: #ffc107; font-weight: bold; }
        .level-4 { color: #20c997; }
        .level-5 { color: #6c757d; }
        .card-header { background-color: #f8f9fa; }
        .table-responsive { max-height: 600px; overflow-y: auto; }
        .btn-group-sm .btn { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
        .alert-dismissible { position: relative; }
        .query-form { background-color: #f8f9fa; border-radius: 0.375rem; padding: 1rem; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-exclamation-triangle text-warning"></i> 告警查询系统
                </h1>
                
                <!-- 查询条件区域 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-search"></i> 查询条件</h5>
                    </div>
                    <div class="card-body">
                        <form id="queryForm" class="query-form">
                            <div class="row g-3">
                                <div class="col-md-2">
                                    <label for="beginTime" class="form-label">开始时间</label>
                                    <input type="datetime-local" class="form-control" id="beginTime" name="beginTime">
                                </div>
                                <div class="col-md-2">
                                    <label for="endTime" class="form-label">结束时间</label>
                                    <input type="datetime-local" class="form-control" id="endTime" name="endTime">
                                </div>
                                <div class="col-md-2">
                                    <label for="alarmStatus" class="form-label">告警状态</label>
                                    <select class="form-select" id="alarmStatus" name="alarmStatus">
                                        <option value="1">告警产生</option>
                                        <option value="2">告警恢复</option>
                                        <option value="3">告警确认</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="alarmCode" class="form-label">告警级别</label>
                                    <select class="form-select" id="alarmCode" name="alarmCode">
                                        <option value="1">紧急告警</option>
                                        <option value="2">重要告警</option>
                                        <option value="3">一般告警</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="pageSize" class="form-label">每页条数</label>
                                    <select class="form-select" id="pageSize" name="pageSize">
                                        <option value="10">10条</option>
                                        <option value="20" selected>20条</option>
                                        <option value="50">50条</option>
                                        <option value="100">100条</option>
                                    </select>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="button" class="btn btn-primary me-2" onclick="queryAlarms()">
                                        <i class="bi bi-search"></i> 查询
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="bi bi-arrow-clockwise"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-bar-chart"></i> 统计信息</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="card border-danger">
                                    <div class="card-body">
                                        <h5 class="card-title text-danger">告警产生</h5>
                                        <h3 class="text-danger" id="activeAlarms">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-success">
                                    <div class="card-body">
                                        <h5 class="card-title text-success">告警恢复</h5>
                                        <h3 class="text-success" id="recoveredAlarms">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-secondary">
                                    <div class="card-body">
                                        <h5 class="card-title text-secondary">告警确认</h5>
                                        <h3 class="text-secondary" id="confirmedAlarms">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card border-info">
                                    <div class="card-body">
                                        <h5 class="card-title text-info">总计</h5>
                                        <h3 class="text-info" id="totalAlarms">0</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 告警列表 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-ul"></i> 告警列表</h5>
                        <div>
                            <span class="badge bg-info me-2">当前页: <span id="currentPage">1</span></span>
                            <span class="badge bg-secondary">总记录: <span id="totalRecords">0</span></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>告警ID</th>
                                        <th>设备ID</th>
                                        <th>站点信息</th>
                                        <th>告警编码</th>
                                        <th>告警状态</th>
                                        <th>告警时间</th>
                                        <th>恢复时间</th>
                                        <th>线路</th>
                                        <th>系统</th>
                                    </tr>
                                </thead>
                                <tbody id="alarmTableBody">
                                    <tr>
                                        <td colspan="9" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <p class="mt-2">正在加载告警数据...</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页控件 -->
                        <nav aria-label="告警列表分页" class="mt-3">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示区域 -->
    <div id="alertContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentAlarms = [];
        let currentPage = 1;
        let totalPages = 1;
        let totalRecords = 0;
        let stationMappings = {}; // 站点映射数据缓存

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认时间范围（最近7天）
            const now = new Date();
            const beginTimeNow = new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000);
            const sevenDaysAgo = new Date(now.getTime() - 2000 * 24 * 60 * 60 * 1000);

            document.getElementById('beginTime').value = formatDateTimeLocal(sevenDaysAgo);
            document.getElementById('endTime').value = formatDateTimeLocal(beginTimeNow);

            // 加载站点映射数据，然后查询告警
            loadStationMappings().then(() => {
                queryAlarms();
            });
        });

        // 加载站点映射数据
        async function loadStationMappings() {
            try {
                const response = await fetch('/ims/station/mapping');
                const result = await response.json();

                if (result.retCode === 0) {
                    stationMappings = result.result || {};
                    console.log('站点映射数据加载成功:', stationMappings);
                } else {
                    console.warn('加载站点映射数据失败:', result.retMessage);
                    stationMappings = {};
                }
            } catch (error) {
                console.error('加载站点映射数据失败:', error);
                stationMappings = {};
            }
        }

        // 获取站点显示名称
        function getStationDisplayName(lineId, stationId) {
            if (!lineId || !stationId) {
                return stationId || '-';
            }

            const lineStations = stationMappings[lineId];
            if (lineStations && lineStations[stationId]) {
                return `${lineStations[stationId]} (${stationId})`;
            }

            return stationId;
        }

        // 查询告警数据
        async function queryAlarms(pageNo = 1) {
            try {
                showLoading();
                
                const formData = new FormData(document.getElementById('queryForm'));
                const params = new URLSearchParams();
                
                // 添加查询参数
                if (formData.get('beginTime')) {
                    params.append('beginTime', formatDateTime(formData.get('beginTime')));
                }
                if (formData.get('endTime')) {
                    params.append('endTime', formatDateTime(formData.get('endTime')));
                }
                if (formData.get('alarmStatus')) {
                    params.append('alarmStatus', formData.get('alarmStatus'));
                }
                if (formData.get('alarmCode')) {
                    params.append('alarmCode', formData.get('alarmCode'));
                }
                
                params.append('pageNo', pageNo);
                params.append('pageSize', formData.get('pageSize') || 20);
                
                const response = await fetch(`/ims/alarm/query?${params.toString()}`);
                const result = await response.json();
                
                if (result.retCode === 0) {

                    console.info('查询告警数据成功', result);
                    let _result = result.result;
                    currentAlarms = _result.dataList || [];
                    currentPage = _result.pageNo || pageNo;
                    totalPages = _result.total/result.pageSize || 1;
                    totalRecords = _result.total || 0;
                    
                    renderAlarmTable(currentAlarms);
                    renderPagination();
                    updateStatistics();
                    showAlert('告警数据加载成功', 'success');
                } else {
                    showAlert('加载告警数据失败: ' + result.retMessage, 'danger');
                    renderEmptyTable();
                }
            } catch (error) {
                console.error('查询告警数据失败:', error);
                showAlert('网络错误，请检查连接', 'danger');
                renderEmptyTable();
            }
        }

        // 渲染告警表格
        function renderAlarmTable(alarms) {
            const tbody = document.getElementById('alarmTableBody');
            
            if (!alarms || alarms.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                            <p class="mt-2">暂无告警数据</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = alarms.map(alarm => `
                <tr>
                    <td>${alarm.alarmId || '-'}</td>
                    <td>${alarm.deviceId || '-'}</td>
                    <td>${getStationDisplayName(alarm.line, alarm.stationId)}</td>
                    <td>${getAlarmCodeBadge(alarm.code)}</td>
                    <td>${getStatusBadge(alarm.status)}</td>
                    <td>${formatDisplayDateTime(alarm.alarmTime)}</td>
                    <td>${alarm.recoveryTime ? formatDisplayDateTime(alarm.recoveryTime) : '-'}</td>
                    <td>${alarm.line || '-'}</td>
                    <td>${alarm.system || '-'}</td>
                </tr>
            `).join('');
        }

        // 显示加载状态
        function showLoading() {
            const tbody = document.getElementById('alarmTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载告警数据...</p>
                    </td>
                </tr>
            `;
        }

        // 渲染空表格
        function renderEmptyTable() {
            const tbody = document.getElementById('alarmTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center text-muted">
                        <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                        <p class="mt-2">加载失败，请重试</p>
                    </td>
                </tr>
            `;
        }

        // 获取状态徽章
        function getStatusBadge(status) {
            const statusMap = {
                1: { text: '告警产生', class: 'bg-danger' },
                2: { text: '告警恢复', class: 'bg-success' },
                3: { text: '告警确认', class: 'bg-secondary' }
            };
            const statusInfo = statusMap[status] || { text: '未知', class: 'bg-secondary' };
            return `<span class="badge ${statusInfo.class}">${statusInfo.text}</span>`;
        }

        // 获取告警编码徽章
        function getAlarmCodeBadge(code) {
            if (!code) {
                return '<span class="badge bg-light text-dark">-</span>';
            }

            const codeNum = parseInt(code);
            switch(codeNum) {
                case 1:
                    return '<span class="badge bg-danger"><i class="bi bi-exclamation-triangle-fill me-1"></i>紧急告警</span>';
                case 2:
                    return '<span class="badge bg-warning text-dark"><i class="bi bi-exclamation-circle-fill me-1"></i>重要告警</span>';
                case 3:
                    return '<span class="badge bg-info"><i class="bi bi-info-circle-fill me-1"></i>一般告警</span>';
                default:
                    return `<span class="badge bg-light text-dark">${code}</span>`;
            }
        }

        // 更新统计信息
        function updateStatistics() {
            const activeCount = currentAlarms.filter(a => a.status === 1).length;
            const recoveredCount = currentAlarms.filter(a => a.status === 2).length;
            const confirmedCount = currentAlarms.filter(a => a.status === 3).length;

            document.getElementById('activeAlarms').textContent = activeCount;
            document.getElementById('recoveredAlarms').textContent = recoveredCount;
            document.getElementById('confirmedAlarms').textContent = confirmedCount;
            document.getElementById('totalAlarms').textContent = totalRecords;
            document.getElementById('currentPage').textContent = currentPage;
            document.getElementById('totalRecords').textContent = totalRecords;
        }

        // 渲染分页控件
        function renderPagination() {
            const pagination = document.getElementById('pagination');

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHtml = '';

            // 上一页按钮
            paginationHtml += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="queryAlarms(${currentPage - 1})" aria-label="上一页">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
            `;

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="queryAlarms(1)">1</a></li>`;
                if (startPage > 2) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationHtml += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="queryAlarms(${i})">${i}</a>
                    </li>
                `;
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="queryAlarms(${totalPages})">${totalPages}</a></li>`;
            }

            // 下一页按钮
            paginationHtml += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="queryAlarms(${currentPage + 1})" aria-label="下一页">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            `;

            pagination.innerHTML = paginationHtml;
        }

        // 重置表单
        function resetForm() {
            document.getElementById('queryForm').reset();

            // 重新设置默认时间范围
            const now = new Date();
            const beginTimeNow = new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000);
            const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

            document.getElementById('beginTime').value = formatDateTimeLocal(sevenDaysAgo);
            document.getElementById('endTime').value = formatDateTimeLocal(beginTimeNow);
            document.getElementById('pageSize').value = '20';

            // 重新查询
            queryAlarms();
        }

        // 格式化日期时间为本地输入格式
        function formatDateTimeLocal(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        // 格式化日期时间为API格式
        function formatDateTime(dateTimeLocal) {
            if (!dateTimeLocal) return '';
            const date = new Date(dateTimeLocal);
            return date.toISOString().replace('T', ' ').substring(0, 23);
        }

        // 格式化显示日期时间
        function formatDisplayDateTime(dateTime) {
            if (!dateTime) return '-';

            let date;
            if (typeof dateTime === 'string') {
                date = new Date(dateTime);
            } else {
                date = new Date(dateTime);
            }

            if (isNaN(date.getTime())) return '-';

            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }

        // 显示提示消息
        function showAlert(message, type = 'info', duration = 5000) {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert_' + Date.now();

            const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="bi bi-${getAlertIcon(type)}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            alertContainer.insertAdjacentHTML('beforeend', alertHtml);

            // 自动消失
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    const bsAlert = new bootstrap.Alert(alertElement);
                    bsAlert.close();
                }
            }, duration);
        }

        // 获取提示图标
        function getAlertIcon(type) {
            const iconMap = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };
            return iconMap[type] || 'info-circle';
        }
    </script>
</body>
</html>
