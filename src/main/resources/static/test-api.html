<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">DeviceDbApi 接口测试</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>GET /db/device/query</h5>
                        <small class="text-muted">查询设备列表</small>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testDeviceQuery()">测试查询接口</button>
                        <div class="mt-3">
                            <label class="form-label">响应结果:</label>
                            <div id="queryResult" class="code-block">点击按钮测试接口...</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>POST /db/device/info/changeReport</h5>
                        <small class="text-muted">设备信息变更报告</small>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">操作类型:</label>
                            <select class="form-select" id="opType">
                                <option value="1">1 - 新增设备</option>
                                <option value="3">3 - 修改设备</option>
                                <option value="2">2 - 删除设备</option>
                            </select>
                        </div>
                        <button class="btn btn-success" onclick="testDeviceChange()">测试变更接口</button>
                        <div class="mt-3">
                            <label class="form-label">响应结果:</label>
                            <div id="changeResult" class="code-block">点击按钮测试接口...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>接口说明</h5>
            </div>
            <div class="card-body">
                <h6>1. 设备查询接口</h6>
                <ul>
                    <li><strong>URL:</strong> GET /db/device/query</li>
                    <li><strong>功能:</strong> 获取所有设备列表</li>
                    <li><strong>返回:</strong> JsonResult格式，包含设备数组</li>
                </ul>
                
                <h6>2. 设备变更接口</h6>
                <ul>
                    <li><strong>URL:</strong> POST /db/device/info/changeReport</li>
                    <li><strong>功能:</strong> 设备信息变更报告（增加、修改、删除）</li>
                    <li><strong>参数:</strong> Device对象，包含opType字段</li>
                    <li><strong>opType值:</strong>
                        <ul>
                            <li>1 - 新增设备</li>
                            <li>2 - 删除设备</li>
                            <li>3 - 修改设备</li>
                        </ul>
                    </li>
                </ul>
                
                <h6>3. 响应格式</h6>
                <pre class="code-block">{
  "retCode": 0,        // 0表示成功，其他表示失败
  "retMessage": "ok",  // 响应消息
  "result": [...]      // 响应数据
}</pre>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试设备查询接口
        async function testDeviceQuery() {
            const resultDiv = document.getElementById('queryResult');
            resultDiv.textContent = '正在请求...';
            
            try {
                const response = await fetch('/db/device/query');
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (result.retCode === 0) {
                    resultDiv.style.color = '#28a745';
                } else {
                    resultDiv.style.color = '#dc3545';
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
                resultDiv.style.color = '#dc3545';
            }
        }
        
        // 测试设备变更接口
        async function testDeviceChange() {
            const resultDiv = document.getElementById('changeResult');
            const opType = parseInt(document.getElementById('opType').value);
            
            resultDiv.textContent = '正在请求...';
            
            // 构造测试数据
            const testDevice = {
                deviceId: 'TEST_' + Date.now(),
                deviceSN: 'SN_' + Date.now(),
                deviceName: '测试设备_' + Date.now(),
                category: '测试设备',
                model: 'TEST_MODEL',
                manufacturer: '测试厂商',
                ip: ['*************'],
                online: 1,
                workMode: 1,
                station: 'TEST_STATION',
                description: '这是一个测试设备',
                opType: opType
            };
            
            try {
                const response = await fetch('/db/device/info/changeReport', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testDevice)
                });
                
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (result.retCode === 0) {
                    resultDiv.style.color = '#28a745';
                } else {
                    resultDiv.style.color = '#dc3545';
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
                resultDiv.style.color = '#dc3545';
            }
        }
    </script>
</body>
</html>
