<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>站点API测试页面</title>
    <link href="js/bootstrap.min.css" rel="stylesheet">
    <style>
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .station-card {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">站点API测试</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>GET /ims/station/mapping</h5>
                        <small class="text-muted">获取站点映射数据</small>
                    </div>
                    <div class="card-body">
                        <form id="mappingForm">
                            <div class="mb-3">
                                <label class="form-label">线路ID (可选):</label>
                                <select class="form-select" id="lineIdMapping" name="lineId">
                                    <option value="">全部线路</option>
                                    <option value="400">400 (4号线)</option>
                                    <option value="600">600 (6号线)</option>
                                </select>
                            </div>
                            <button type="button" class="btn btn-primary" onclick="testStationMapping()">测试映射接口</button>
                        </form>
                        
                        <div class="mt-3">
                            <label class="form-label">响应结果:</label>
                            <div id="mappingResult" class="code-block">点击按钮测试接口...</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>GET /ims/station/name</h5>
                        <small class="text-muted">获取站点名称</small>
                    </div>
                    <div class="card-body">
                        <form id="nameForm">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">线路ID:</label>
                                    <select class="form-select" id="lineIdName" name="lineId" required>
                                        <option value="">请选择线路</option>
                                        <option value="400">400 (4号线)</option>
                                        <option value="600">600 (6号线)</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">站点ID:</label>
                                    <input type="text" class="form-control" id="stationIdName" name="stationId" 
                                           placeholder="例: 1" required>
                                </div>
                            </div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-success" onclick="testStationName()">测试名称接口</button>
                            </div>
                        </form>
                        
                        <div class="mt-3">
                            <label class="form-label">响应结果:</label>
                            <div id="nameResult" class="code-block">点击按钮测试接口...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 站点映射展示 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>站点映射展示</h5>
            </div>
            <div class="card-body">
                <div class="row" id="stationDisplay">
                    <div class="col-12 text-center text-muted">
                        <p>点击"测试映射接口"按钮加载站点数据</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>接口说明</h5>
            </div>
            <div class="card-body">
                <h6>1. 站点映射接口</h6>
                <ul>
                    <li><strong>URL:</strong> GET /ims/station/mapping</li>
                    <li><strong>参数:</strong> lineId (可选) - 线路ID，不传则返回所有线路</li>
                    <li><strong>功能:</strong> 获取站点ID到站点名称的映射关系</li>
                    <li><strong>返回:</strong> JsonResult格式，包含站点映射数据</li>
                </ul>
                
                <h6>2. 站点名称接口</h6>
                <ul>
                    <li><strong>URL:</strong> GET /ims/station/name</li>
                    <li><strong>参数:</strong> 
                        <ul>
                            <li><code>lineId</code> - 线路ID (必填)</li>
                            <li><code>stationId</code> - 站点ID (必填)</li>
                        </ul>
                    </li>
                    <li><strong>功能:</strong> 根据线路ID和站点ID获取站点名称</li>
                    <li><strong>返回:</strong> JsonResult格式，包含站点信息</li>
                </ul>
                
                <h6>3. 线路说明</h6>
                <ul>
                    <li><code>400</code> - 厦门地铁4号线</li>
                    <li><code>600</code> - 厦门地铁6号线</li>
                </ul>
                
                <h6>4. 响应格式</h6>
                <pre class="code-block">{
  "retCode": 0,
  "retMessage": "获取站点映射数据成功",
  "result": {
    "400": {
      "1": "软三东站",
      "2": "后溪站",
      "3": "厦门北站",
      ...
    },
    "600": {
      "1": "龙江明珠站",
      "2": "石美站",
      "3": "角江路站",
      ...
    }
  }
}</pre>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试站点映射接口
        async function testStationMapping() {
            const resultDiv = document.getElementById('mappingResult');
            const lineId = document.getElementById('lineIdMapping').value;
            
            resultDiv.textContent = '正在请求...';
            resultDiv.style.color = '#000';
            
            try {
                let url = '/ims/station/mapping';
                if (lineId) {
                    url += `?lineId=${lineId}`;
                }
                
                const response = await fetch(url);
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (result.retCode === 0) {
                    resultDiv.style.color = '#28a745';
                    displayStationMappings(result.result);
                } else {
                    resultDiv.style.color = '#dc3545';
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
                resultDiv.style.color = '#dc3545';
            }
        }
        
        // 测试站点名称接口
        async function testStationName() {
            const resultDiv = document.getElementById('nameResult');
            const lineId = document.getElementById('lineIdName').value;
            const stationId = document.getElementById('stationIdName').value;
            
            if (!lineId || !stationId) {
                resultDiv.textContent = '请填写线路ID和站点ID';
                resultDiv.style.color = '#dc3545';
                return;
            }
            
            resultDiv.textContent = '正在请求...';
            resultDiv.style.color = '#000';
            
            try {
                const url = `/ims/station/name?lineId=${lineId}&stationId=${stationId}`;
                
                const response = await fetch(url);
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (result.retCode === 0) {
                    resultDiv.style.color = '#28a745';
                } else {
                    resultDiv.style.color = '#dc3545';
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
                resultDiv.style.color = '#dc3545';
            }
        }
        
        // 显示站点映射数据
        function displayStationMappings(mappings) {
            const displayDiv = document.getElementById('stationDisplay');
            
            if (!mappings || Object.keys(mappings).length === 0) {
                displayDiv.innerHTML = '<div class="col-12 text-center text-muted"><p>暂无站点数据</p></div>';
                return;
            }
            
            let html = '';
            
            Object.keys(mappings).forEach(lineId => {
                const stations = mappings[lineId];
                const lineName = lineId === '400' ? '4号线' : lineId === '600' ? '6号线' : `${lineId}号线`;
                
                html += `
                    <div class="col-md-6 station-card">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">${lineName} (${lineId})</h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-2">
                `;
                
                Object.keys(stations).forEach(stationId => {
                    const stationName = stations[stationId];
                    html += `
                        <div class="col-6">
                            <small class="badge bg-light text-dark">${stationId}: ${stationName}</small>
                        </div>
                    `;
                });
                
                html += `
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            displayDiv.innerHTML = html;
        }
        
        // 页面加载完成后自动测试映射接口
        document.addEventListener('DOMContentLoaded', function() {
            testStationMapping();
        });
    </script>
</body>
</html>
