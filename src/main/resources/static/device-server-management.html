<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备服务器管理</title>
    <link href="js/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="bootstrap-icons-1.13.1/bootstrap-icons.css">
    <style>
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .status-badge {
            font-size: 0.8rem;
        }
        .server-card {
            transition: transform 0.2s;
        }
        .server-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .sync-btn {
            min-width: 120px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/ims/">
                <i class="bi bi-server me-2"></i>设备服务器管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/ims/">
                    <i class="bi bi-house"></i> 返回主页
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题和操作 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2 class="fw-bold">
                    <i class="bi bi-server text-success me-2"></i>
                    设备服务器管理
                </h2>
                <p class="text-muted">管理室分网管服务器设备，支持查询和同步到综合网管系统</p>
            </div>
            <div class="col-md-4 text-end">
                <button type="button" class="btn btn-primary" onclick="loadServerList()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新列表
                </button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-server feature-icon"></i>
                        <h4 id="totalServers">0</h4>
                        <p class="mb-0">总服务器数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-check-circle feature-icon"></i>
                        <h4 id="onlineServers">0</h4>
                        <p class="mb-0">在线服务器</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-x-circle feature-icon"></i>
                        <h4 id="offlineServers">0</h4>
                        <p class="mb-0">离线服务器</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-question-circle feature-icon"></i>
                        <h4 id="unknownServers">0</h4>
                        <p class="mb-0">未知状态</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 服务器列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul me-2"></i>服务器设备列表
                </h5>
            </div>
            <div class="card-body">
                <div id="serverListContainer">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载服务器数据...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作结果提示 -->
        <div id="alertContainer" class="mt-3"></div>
    </div>

    <!-- 服务器详情模态框 -->
    <div class="modal fade" id="serverDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-info-circle me-2"></i>服务器详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="serverDetailContent">
                    <!-- 详情内容将通过JavaScript动态填充 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑服务器模态框 -->
    <div class="modal fade" id="editServerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-pencil me-2"></i>编辑服务器
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editServerForm">
                        <input type="hidden" id="editDeviceId" name="deviceId">

                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="editDeviceName" class="form-label">设备名称</label>
                                <input type="text" class="form-control" id="editDeviceName" name="deviceName">
                            </div>
                            <div class="col-md-6">
                                <label for="editDeviceSN" class="form-label">设备SN</label>
                                <input type="text" class="form-control" id="editDeviceSN" name="deviceSN">
                            </div>
                            <div class="col-md-6">
                                <label for="editCategory" class="form-label">设备类别</label>
                                <input type="text" class="form-control" id="editCategory" name="category">
                            </div>
                            <div class="col-md-6">
                                <label for="editModel" class="form-label">设备型号</label>
                                <input type="text" class="form-control" id="editModel" name="model">
                            </div>
                            <div class="col-md-6">
                                <label for="editManufacturer" class="form-label">制造商</label>
                                <input type="text" class="form-control" id="editManufacturer" name="manufacturer">
                            </div>
                            <div class="col-md-6">
                                <label for="editDeviceVersion" class="form-label">设备版本</label>
                                <input type="text" class="form-control" id="editDeviceVersion" name="deviceVersion">
                            </div>
                            <div class="col-md-6">
                                <label for="editLine" class="form-label">线路</label>
                                <input type="text" class="form-control" id="editLine" name="line">
                            </div>
                            <div class="col-md-6">
                                <label for="editStation" class="form-label">站点</label>
                                <input type="text" class="form-control" id="editStation" name="station">
                            </div>
                            <div class="col-md-6">
                                <label for="editEngineRoom" class="form-label">机房号</label>
                                <input type="text" class="form-control" id="editEngineRoom" name="engineRoom">
                            </div>
                            <div class="col-md-6">
                                <label for="editCabinet" class="form-label">机柜号</label>
                                <input type="text" class="form-control" id="editCabinet" name="cabinet">
                            </div>
                            <div class="col-md-6">
                                <label for="editFrame" class="form-label">机架号</label>
                                <input type="text" class="form-control" id="editFrame" name="frame">
                            </div>
                            <div class="col-md-6">
                                <label for="editDeviceIp" class="form-label">IP地址</label>
                                <input type="text" class="form-control" id="editDeviceIp" name="deviceIp"
                                       placeholder="多个IP用逗号分隔">
                            </div>
                            <div class="col-md-6">
                                <label for="editOnline" class="form-label">在线状态</label>
                                <select class="form-select" id="editOnline" name="online">
                                    <option value="1">在线</option>
                                    <option value="2">离线</option>
                                    <option value="3">待机</option>
                                    <option value="4">关机</option>
                                    <option value="5">未知</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="editWorkMode" class="form-label">工作模式</label>
                                <select class="form-select" id="editWorkMode" name="workMode">
                                    <option value="1">主用</option>
                                    <option value="2">备用</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="editDescription" class="form-label">描述</label>
                                <textarea class="form-control" id="editDescription" name="description" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveServerChanges()">
                        <i class="bi bi-check"></i> 保存修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentServers = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadServerList();
        });

        // 加载服务器列表
        async function loadServerList() {
            try {
                showLoading();
                
                const response = await fetch('/ims/deviceServer/query');
                const result = await response.json();
                
                if (result.retCode === 0) {
                    currentServers = result.result.dataList || [];
                    renderServerList(currentServers);
                    updateStatistics();
                    showAlert('服务器数据加载成功', 'success');
                } else {
                    showAlert('加载服务器数据失败: ' + result.retMessage, 'danger');
                    renderEmptyList();
                }
            } catch (error) {
                console.error('加载服务器数据失败:', error);
                showAlert('网络错误，请检查连接', 'danger');
                renderEmptyList();
            }
        }

        // 渲染服务器列表
        function renderServerList(servers) {
            const container = document.getElementById('serverListContainer');
            
            if (!servers || servers.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5 text-muted">
                        <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                        <p class="mt-2">暂无服务器数据</p>
                    </div>
                `;
                return;
            }

            const serverCards = servers.map(server => `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card server-card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h6 class="card-title mb-0">
                                    <i class="bi bi-server text-primary me-2"></i>
                                    ${server.deviceName || server.deviceId || '未命名服务器'}
                                </h6>
                                ${getStatusBadge(server.online)}
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted d-block">设备ID: ${server.deviceId || '-'}</small>
                                <small class="text-muted d-block">设备SN: ${server.deviceSN || '-'}</small>
                                <small class="text-muted d-block">型号: ${server.model || '-'}</small>
                                <small class="text-muted d-block">IP地址: ${getIpDisplay(server.ip)}</small>
                                <small class="text-muted d-block">线路: ${server.line || '-'}</small>
                                <small class="text-muted d-block">站点: ${server.station || '-'}</small>
                            </div>
                            
                            <div class="d-flex gap-1 mb-2">
                                <button class="btn btn-outline-primary btn-sm flex-fill"
                                        onclick="showServerDetail('${server.deviceId}')">
                                    <i class="bi bi-eye"></i> 详情
                                </button>
                                <button class="btn btn-warning btn-sm flex-fill"
                                        onclick="showEditServerModal('${server.deviceId}')"
                                        ${!server.deviceId ? 'disabled' : ''}>
                                    <i class="bi bi-pencil"></i> 修改
                                </button>
                            </div>
                            <div class="d-flex gap-1">
                                <button class="btn btn-success btn-sm flex-fill"
                                        onclick="syncToZhwg('${server.deviceId}', 1)"
                                        ${!server.deviceId ? 'disabled' : ''}>
                                    <i class="bi bi-cloud-upload"></i> 同步
                                </button>
                                <button class="btn btn-danger btn-sm flex-fill"
                                        onclick="deleteServer('${server.deviceId}')"
                                        ${!server.deviceId ? 'disabled' : ''}>
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = `<div class="row">${serverCards}</div>`;
        }

        // 显示加载状态
        function showLoading() {
            const container = document.getElementById('serverListContainer');
            container.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载服务器数据...</p>
                </div>
            `;
        }

        // 渲染空列表
        function renderEmptyList() {
            const container = document.getElementById('serverListContainer');
            container.innerHTML = `
                <div class="text-center py-5 text-muted">
                    <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
                    <p class="mt-2">加载失败，请重试</p>
                </div>
            `;
        }

        // 获取状态徽章
        function getStatusBadge(status) {
            const statusMap = {
                1: { text: '在线', class: 'bg-success' },
                2: { text: '离线', class: 'bg-danger' },
                3: { text: '待机', class: 'bg-warning' },
                4: { text: '关机', class: 'bg-secondary' },
                5: { text: '未知', class: 'bg-info' }
            };
            const statusInfo = statusMap[status] || { text: '未知', class: 'bg-secondary' };
            return `<span class="badge ${statusInfo.class} status-badge">${statusInfo.text}</span>`;
        }

        // 获取IP地址显示
        function getIpDisplay(ipList) {
            if (!ipList || ipList.length === 0) {
                return '-';
            }
            return ipList.join(', ');
        }

        // 更新统计信息
        function updateStatistics() {
            const total = currentServers.length;
            const online = currentServers.filter(s => s.online === 1).length;
            const offline = currentServers.filter(s => s.online === 2).length;
            const unknown = currentServers.filter(s => s.online === 5 || !s.online).length;

            document.getElementById('totalServers').textContent = total;
            document.getElementById('onlineServers').textContent = online;
            document.getElementById('offlineServers').textContent = offline;
            document.getElementById('unknownServers').textContent = unknown;
        }

        // 显示服务器详情
        function showServerDetail(deviceId) {
            const server = currentServers.find(s => s.deviceId === deviceId);
            if (!server) {
                showAlert('未找到服务器信息', 'warning');
                return;
            }

            const detailContent = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>设备ID:</td><td>${server.deviceId || '-'}</td></tr>
                            <tr><td>设备名称:</td><td>${server.deviceName || '-'}</td></tr>
                            <tr><td>设备SN:</td><td>${server.deviceSN || '-'}</td></tr>
                            <tr><td>类别:</td><td>${server.category || '-'}</td></tr>
                            <tr><td>型号:</td><td>${server.model || '-'}</td></tr>
                            <tr><td>制造商:</td><td>${server.manufacturer || '-'}</td></tr>
                            <tr><td>版本:</td><td>${server.deviceVersion || '-'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>位置信息</h6>
                        <table class="table table-sm">
                            <tr><td>线路:</td><td>${server.line || '-'}</td></tr>
                            <tr><td>站点:</td><td>${server.station || '-'}</td></tr>
                            <tr><td>机房号:</td><td>${server.engineRoom || '-'}</td></tr>
                            <tr><td>机柜号:</td><td>${server.cabinet || '-'}</td></tr>
                            <tr><td>机架号:</td><td>${server.frame || '-'}</td></tr>
                            <tr><td>经度:</td><td>${server.longitude || '-'}</td></tr>
                            <tr><td>纬度:</td><td>${server.latitude || '-'}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>状态信息</h6>
                        <table class="table table-sm">
                            <tr><td>在线状态:</td><td>${getStatusBadge(server.online)}</td></tr>
                            <tr><td>工作模式:</td><td>${getWorkModeBadge(server.workMode)}</td></tr>
                            <tr><td>IP地址:</td><td>${getIpDisplay(server.ip)}</td></tr>
                            <tr><td>描述:</td><td>${server.description || '-'}</td></tr>
                        </table>
                    </div>
                </div>
            `;

            document.getElementById('serverDetailContent').innerHTML = detailContent;
            new bootstrap.Modal(document.getElementById('serverDetailModal')).show();
        }

        // 获取工作模式徽章
        function getWorkModeBadge(workMode) {
            switch(workMode) {
                case 1:
                    return '<span class="badge bg-primary">主用</span>';
                case 2:
                    return '<span class="badge bg-secondary">备用</span>';
                default:
                    return '<span class="badge bg-light text-dark">未知</span>';
            }
        }

        // 同步到综合网管
        async function syncToZhwg(deviceId, opType = 1) {
            if (!deviceId) {
                showAlert('设备ID不能为空', 'warning');
                return;
            }

            if (!confirm('确定要将此服务器同步到综合网管系统吗？')) {
                return;
            }

            try {
                const formData = new FormData();
                formData.append('deviceId', deviceId);
                formData.append('opType', opType);

                const response = await fetch('/ims/deviceServer/update/to/zhwg', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.retCode === 0) {
                    showAlert('服务器同步成功', 'success');
                } else {
                    showAlert('服务器同步失败: ' + result.retMessage, 'danger');
                }
            } catch (error) {
                console.error('同步服务器失败:', error);
                showAlert('网络错误，同步失败', 'danger');
            }
        }

        // 显示编辑服务器模态框
        function showEditServerModal(deviceId) {
            const server = currentServers.find(s => s.deviceId === deviceId);
            if (!server) {
                showAlert('未找到服务器信息', 'warning');
                return;
            }

            // 填充表单数据
            document.getElementById('editDeviceId').value = server.deviceId || '';
            document.getElementById('editDeviceName').value = server.deviceName || '';
            document.getElementById('editDeviceSN').value = server.deviceSN || '';
            document.getElementById('editCategory').value = server.category || '';
            document.getElementById('editModel').value = server.model || '';
            document.getElementById('editManufacturer').value = server.manufacturer || '';
            document.getElementById('editDeviceVersion').value = server.deviceVersion || '';
            document.getElementById('editLine').value = server.line || '';
            document.getElementById('editStation').value = server.station || '';
            document.getElementById('editEngineRoom').value = server.engineRoom || '';
            document.getElementById('editCabinet').value = server.cabinet || '';
            document.getElementById('editFrame').value = server.frame || '';
            document.getElementById('editDeviceIp').value = server.ip ? server.ip.join(', ') : '';
            document.getElementById('editOnline').value = server.online || '5';
            document.getElementById('editWorkMode').value = server.workMode || '1';
            document.getElementById('editDescription').value = server.description || '';

            // 显示模态框
            new bootstrap.Modal(document.getElementById('editServerModal')).show();
        }

        // 保存服务器修改
        async function saveServerChanges() {
            const form = document.getElementById('editServerForm');
            const formData = new FormData(form);

            // 添加操作类型为修改
            formData.append('opType', '3');

            // 处理IP地址
            const deviceIp = formData.get('deviceIp');
            if (deviceIp) {
                formData.delete('deviceIp');
                const ipList = deviceIp.split(',').map(ip => ip.trim()).filter(ip => ip);
                ipList.forEach((ip, index) => {
                    formData.append(`ip[${index}]`, ip);
                });
            }

            try {
                const response = await fetch('/ims/deviceServer/update/to/zhwg', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.retCode === 0) {
                    showAlert('服务器修改成功', 'success');
                    // 关闭模态框
                    bootstrap.Modal.getInstance(document.getElementById('editServerModal')).hide();
                    // 重新加载服务器列表
                    loadServerList();
                } else {
                    showAlert('服务器修改失败: ' + result.retMessage, 'danger');
                }
            } catch (error) {
                console.error('修改服务器失败:', error);
                showAlert('网络错误，修改失败', 'danger');
            }
        }

        // 删除服务器
        async function deleteServer(deviceId) {
            if (!deviceId) {
                showAlert('设备ID不能为空', 'warning');
                return;
            }

            if (!confirm('确定要删除此服务器吗？此操作不可撤销！')) {
                return;
            }

            try {
                const formData = new FormData();
                formData.append('deviceId', deviceId);
                formData.append('opType', '2'); // 删除操作

                const response = await fetch('/ims/deviceServer/update/to/zhwg', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.retCode === 0) {
                    showAlert('服务器删除成功', 'success');
                    // 重新加载服务器列表
                    loadServerList();
                } else {
                    showAlert('服务器删除失败: ' + result.retMessage, 'danger');
                }
            } catch (error) {
                console.error('删除服务器失败:', error);
                showAlert('网络错误，删除失败', 'danger');
            }
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();
            
            const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.innerHTML = alertHtml;
            
            // 3秒后自动隐藏
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    const alert = new bootstrap.Alert(alertElement);
                    alert.close();
                }
            }, 3000);
        }
    </script>
</body>
</html>
