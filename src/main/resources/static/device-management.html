<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备管理系统</title>
    <link href="js/bootstrap.min.css" rel="stylesheet">
    <link href="bootstrap-icons-1.13.1/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-online {
            color: #28a745;
        }

        .status-offline {
            color: #dc3545;
        }

        .status-standby {
            color: #ffc107;
        }

        .status-shutdown {
            color: #6c757d;
        }

        .status-unknown {
            color: #17a2b8;
        }

        .card-header {
            background-color: #f8f9fa;
        }

        .table-responsive {
            max-height: 600px;
            overflow-y: auto;
        }

        .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .modal-lg {
            max-width: 800px;
        }

        .form-label {
            font-weight: 600;
        }

        .alert-dismissible {
            position: relative;
        }
    </style>
</head>
<body>
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4"><i class="bi bi-router"></i> 设备管理系统</h1>

            <!-- 操作按钮区域 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-gear"></i> 操作面板</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-primary me-2" onclick="updateDeviceName()">
                                <i class="bi bi-arrow-clockwise"></i> 初始化设备名称
                            </button>
                            <button type="button" class="btn btn-primary me-2" onclick="loadDeviceList()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新设备列表
                            </button>
                            <button type="button" class="btn btn-success me-2" onclick="showAddDeviceModal()">
                                <i class="bi bi-plus-circle"></i> 添加设备
                            </button>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge bg-info me-2">总设备数: <span id="totalDevices">0</span></span>
                            <span class="badge bg-success me-2">在线: <span id="onlineDevices">0</span></span>
                            <span class="badge bg-danger">离线: <span id="offlineDevices">0</span></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备列表 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-list-ul"></i> 设备列表</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                            <tr>
                                <th>设备ID</th>
                                <th>设备名称</th>
                                <th>设备SN</th>
                                <th>类别</th>
                                <th>型号</th>
                                <th>制造商</th>
                                <th>IP地址</th>
                                <th>在线状态</th>
                                <th>工作模式</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody id="deviceTableBody">
                            <tr>
                                <td colspan="10" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载设备数据...</p>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加/编辑设备模态框 -->
<div class="modal fade" id="deviceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deviceModalTitle">添加设备</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="deviceForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="deviceId" class="form-label">设备ID</label>
                                <input type="text" class="form-control" id="deviceId" name="deviceId" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="deviceSN" class="form-label">设备SN *</label>
                                <input type="text" class="form-control" id="deviceSN" name="deviceSN" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="deviceName" class="form-label">设备名称 *</label>
                                <input type="text" class="form-control" id="deviceName" name="deviceName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category" class="form-label">设备类别 *</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">请选择类别</option>
                                    <option value="4号线监控主机">4号线监控主机</option>
                                    <option value="4号线室分天线">4号线室分天线</option>

                                    <option value="6号线监控主机">6号线监控主机</option>
                                    <option value="6号线室分天线">6号线室分天线</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="model" class="form-label">设备型号</label>
                                <input type="text" class="form-control" id="model" name="model">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="manufacturer" class="form-label">制造商</label>
                                <input type="text" class="form-control" id="manufacturer" name="manufacturer">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="deviceIp" class="form-label">IP地址 *</label>
                                <input type="text" class="form-control" id="deviceIp" name="deviceIp"
                                       placeholder="例: *************" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="online" class="form-label">在线状态</label>
                                <select class="form-select" id="online" name="online">
                                    <option value="1">在线</option>
                                    <option value="2">离线</option>
                                    <option value="3">待机</option>
                                    <option value="4">关机</option>
                                    <option value="5">未知</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="workMode" class="form-label">工作模式</label>
                                <select class="form-select" id="workMode" name="workMode">
                                    <option value="1">主用</option>
                                    <option value="2">备用</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="station" class="form-label">站点</label>
                                <input type="text" class="form-control" id="station" name="station">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">设备描述</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveDevice()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 消息提示区域 -->
<div id="alertContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>

<script src="js/bootstrap.bundle.min.js"></script>
<script>
    // 全局变量
    let currentDevices = [];
    let currentEditingDevice = null;
    let deviceModal;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        deviceModal = new bootstrap.Modal(document.getElementById('deviceModal'));
        loadDeviceList();
    });

    // 加载设备列表
    async function loadDeviceList() {
        try {
            showLoading();
            const response = await fetch('/ims/db/device/query');
            const result = await response.json();

            if (result.retCode === 0) {
                currentDevices = result.result || [];
                renderDeviceTable(currentDevices);
                updateStatistics();
                showAlert('设备列表加载成功', 'success');
            } else {
                showAlert('加载设备列表失败: ' + result.retMessage, 'danger');
                renderEmptyTable();
            }
        } catch (error) {
            console.error('加载设备列表失败:', error);
            showAlert('网络错误，请检查连接', 'danger');
            renderEmptyTable();
        }
    }

    /**
     * 更新设备名称
     * @returns {Promise<void>}
     */
    async function updateDeviceName() {
        try {
            showLoading();
            const response = await fetch('/ims/db/device/update');
            const result = await response.json();

            if (result.retCode === 200) {
                currentDevices = result.result || [];
                renderDeviceTable(currentDevices);
                updateStatistics();
                showAlert('设备列表加载成功', 'success');
            } else {
                showAlert('加载设备列表失败: ' + result.retMessage, 'danger');
                renderEmptyTable();
            }
        } catch (error) {
            console.error('加载设备列表失败:', error);
            showAlert('网络错误，请检查连接', 'danger');
            renderEmptyTable();
        }
    }

    // 渲染设备表格
    function renderDeviceTable(devices) {
        const tbody = document.getElementById('deviceTableBody');

        if (!devices || devices.length === 0) {
            tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center text-muted">
                            <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                            <p class="mt-2">暂无设备数据</p>
                        </td>
                    </tr>
                `;
            return;
        }

        tbody.innerHTML = devices.map(device => `
                <tr>
                    <td>${device.deviceId || '-'}</td>
                    <td>${device.deviceName || '-'}</td>
                    <td>${device.deviceSN || '-'}</td>
                    <td>${device.category || '-'}</td>
                    <td>${device.model || '-'}</td>
                    <td>${device.manufacturer || '-'}</td>
                    <td>${device.deviceIp || (device.ip && device.ip.length > 0 ? device.ip[0] : '-')}</td>
                    <td>${getStatusBadge(device.online)}</td>
                    <td>${getWorkModeBadge(device.workMode)}</td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="editDevice('${device.deviceId}')" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteDevice('${device.deviceId}')" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
    }

    // 显示加载状态
    function showLoading() {
        const tbody = document.getElementById('deviceTableBody');
        tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载设备数据...</p>
                    </td>
                </tr>
            `;
    }

    // 渲染空表格
    function renderEmptyTable() {
        const tbody = document.getElementById('deviceTableBody');
        tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center text-muted">
                        <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                        <p class="mt-2">加载失败，请重试</p>
                    </td>
                </tr>
            `;
    }

    // 获取状态徽章
    function getStatusBadge(status) {
        const statusMap = {
            1: {text: '在线', class: 'bg-success'},
            2: {text: '离线', class: 'bg-danger'},
            3: {text: '待机', class: 'bg-warning'},
            4: {text: '关机', class: 'bg-secondary'},
            5: {text: '未知', class: 'bg-info'}
        };
        const statusInfo = statusMap[status] || {text: '未知', class: 'bg-secondary'};
        return `<span class="badge ${statusInfo.class}">${statusInfo.text}</span>`;
    }

    // 获取工作模式徽章
    function getWorkModeBadge(workMode) {
        const modeMap = {
            1: {text: '主用', class: 'bg-primary'},
            2: {text: '备用', class: 'bg-secondary'}
        };
        const modeInfo = modeMap[workMode] || {text: '-', class: 'bg-light text-dark'};
        return `<span class="badge ${modeInfo.class}">${modeInfo.text}</span>`;
    }

    // 更新统计信息
    function updateStatistics() {
        const total = currentDevices.length;
        const online = currentDevices.filter(d => d.online === 1).length;
        const offline = currentDevices.filter(d => d.online != 1).length;

        document.getElementById('totalDevices').textContent = total;
        document.getElementById('onlineDevices').textContent = online;
        document.getElementById('offlineDevices').textContent = offline;
    }

    // 显示添加设备模态框
    function showAddDeviceModal() {
        currentEditingDevice = null;
        document.getElementById('deviceModalTitle').textContent = '添加设备';
        document.getElementById('deviceForm').reset();
        document.getElementById('deviceId').value = generateDeviceId();
        deviceModal.show();
    }

    // 编辑设备
    function editDevice(deviceId) {
        const device = currentDevices.find(d => d.deviceId === deviceId);
        if (!device) {
            showAlert('设备不存在', 'warning');
            return;
        }

        currentEditingDevice = device;
        document.getElementById('deviceModalTitle').textContent = '编辑设备';

        // 填充表单数据
        document.getElementById('deviceId').value = device.deviceId || '';
        document.getElementById('deviceSN').value = device.deviceSN || '';
        document.getElementById('deviceName').value = device.deviceName || '';
        document.getElementById('category').value = device.category || '';
        document.getElementById('model').value = device.model || '';
        document.getElementById('manufacturer').value = device.manufacturer || '';
        document.getElementById('deviceIp').value = device.deviceIp || (device.ip && device.ip.length > 0 ? device.ip[0] : '');
        document.getElementById('online').value = device.online || 1;
        document.getElementById('workMode').value = device.workMode || 1;
        document.getElementById('station').value = device.station || '';
        document.getElementById('description').value = device.description || '';

        deviceModal.show();
    }

    // 保存设备
    async function saveDevice() {
        const form = document.getElementById('deviceForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const formData = new FormData(form);
        const deviceData = {
            deviceId: formData.get('deviceId'),
            deviceSN: formData.get('deviceSN'),
            deviceName: formData.get('deviceName'),
            category: formData.get('category'),
            model: formData.get('model'),
            manufacturer: formData.get('manufacturer'),
            ip: [formData.get('deviceIp')],
            online: parseInt(formData.get('online')),
            workMode: parseInt(formData.get('workMode')),
            station: formData.get('station'),
            description: formData.get('description'),
            opType: currentEditingDevice ? 3 : 1 // 1=新增, 3=修改
        };

        try {
            const response = await fetch('/ims/db/device/info/changeReport', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(deviceData)
            });

            const result = await response.json();

            if (result.retCode === 0) {
                showAlert(currentEditingDevice ? '设备更新成功' : '设备添加成功', 'success');
                deviceModal.hide();
                loadDeviceList(); // 重新加载列表
            } else {
                showAlert('操作失败: ' + result.retMessage, 'danger');
            }
        } catch (error) {
            console.error('保存设备失败:', error);
            showAlert('网络错误，请检查连接', 'danger');
        }
    }

    // 删除设备
    async function deleteDevice(deviceId) {
        const device = currentDevices.find(d => d.deviceId === deviceId);
        if (!device) {
            showAlert('设备不存在', 'warning');
            return;
        }

        if (!confirm(`确定要删除设备 "${device.deviceName}" 吗？此操作不可撤销。`)) {
            return;
        }

        const deviceData = {
            deviceId: device.deviceId,
            deviceSN: device.deviceSN,
            opType: 2 // 2=删除
        };

        try {
            const response = await fetch('/ims/db/device/info/changeReport', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(deviceData)
            });

            const result = await response.json();

            if (result.retCode === 0) {
                showAlert('设备删除成功', 'success');
                loadDeviceList(); // 重新加载列表
            } else {
                showAlert('删除失败: ' + result.retMessage, 'danger');
            }
        } catch (error) {
            console.error('删除设备失败:', error);
            showAlert('网络错误，请检查连接', 'danger');
        }
    }

    // 生成设备ID
    function generateDeviceId() {
        return 'DEV_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5).toUpperCase();
    }

    // 显示提示消息
    function showAlert(message, type = 'info', duration = 5000) {
        const alertContainer = document.getElementById('alertContainer');
        const alertId = 'alert_' + Date.now();

        const alertHtml = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="bi bi-${getAlertIcon(type)}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

        alertContainer.insertAdjacentHTML('beforeend', alertHtml);

        // 自动消失
        setTimeout(() => {
            const alertElement = document.getElementById(alertId);
            if (alertElement) {
                const bsAlert = new bootstrap.Alert(alertElement);
                bsAlert.close();
            }
        }, duration);
    }

    // 获取提示图标
    function getAlertIcon(type) {
        const iconMap = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return iconMap[type] || 'info-circle';
    }
</script>
</body>
</html>
