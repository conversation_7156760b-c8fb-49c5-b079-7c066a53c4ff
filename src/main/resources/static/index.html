<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>厦门室分监测系统</title>
    <link href="js/bootstrap.min.css" rel="stylesheet">
    <link href="bootstrap-icons-1.13.1/bootstrap-icons.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-building"></i> 厦门室分监测系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ims/device-management.html">设备管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ims/alarm-query.html">告警查询</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/swagger-ui/">API文档</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="hero-section text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">厦门室分监测系统</h1>
            <p class="lead mb-4">专业的室内分布系统监控与管理平台</p>
            <a href="/device-management.html" class="btn btn-light btn-lg me-3">
                <i class="bi bi-router"></i> 设备管理
            </a>
            <a href="/ims/alarm-query.html" class="btn btn-outline-light btn-lg">
                <i class="bi bi-exclamation-triangle"></i> 告警查询
            </a>
        </div>
    </div>

    <!-- 功能特性 -->
    <div class="container my-5">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="fw-bold">系统功能</h2>
                <p class="text-muted">提供全面的设备监控和管理功能</p>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-4">
                <div class="card feature-card h-100 text-center p-4">
                    <div class="card-body">
                        <i class="bi bi-router feature-icon text-primary"></i>
                        <h5 class="card-title">设备管理</h5>
                        <p class="card-text">实时监控设备状态，支持设备的增加、删除、修改操作，提供完整的设备生命周期管理。</p>
                        <a href="/ims/device-management.html" class="btn btn-primary">
                            <i class="bi bi-arrow-right"></i> 查看详情
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card feature-card h-100 text-center p-4">
                    <div class="card-body">
                        <i class="bi bi-exclamation-triangle feature-icon text-warning"></i>
                        <h5 class="card-title">告警查询</h5>
                        <p class="card-text">查询和管理系统告警信息，支持按时间、状态等条件筛选，自动映射站点名称。</p>
                        <a href="/ims/alarm-query.html" class="btn btn-warning">
                            <i class="bi bi-arrow-right"></i> 查看详情
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card feature-card h-100 text-center p-4">
                    <div class="card-body">
                        <i class="bi bi-geo-alt feature-icon text-info"></i>
                        <h5 class="card-title">站点管理</h5>
                        <p class="card-text">管理地铁线路站点信息，提供4号线和6号线的站点ID与名称映射关系。</p>
                        <a href="/ims/test-station-api.html" class="btn btn-info">
                            <i class="bi bi-arrow-right"></i> 查看详情
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API信息 -->
    <div class="bg-light py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-8 mx-auto text-center">
                    <h3 class="fw-bold mb-4">API接口信息</h3>
                    <p class="mb-4">系统提供完整的REST API接口，支持设备数据查询和设备信息变更报告</p>

                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">GET /db/device/query</h6>
                                    <p class="card-text small">查询设备数据列表</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title text-success">POST /db/device/info/changeReport</h6>
                                    <p class="card-text small">设备信息变更报告</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title text-warning">GET /alarm/query</h6>
                                    <p class="card-text small">查询告警记录数据</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title text-info">GET /station/mapping</h6>
                                    <p class="card-text small">获取站点映射数据</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title text-info">GET /station/name</h6>
                                    <p class="card-text small">获取站点名称</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <a href="/swagger-ui/" class="btn btn-outline-primary" target="_blank">
                            <i class="bi bi-book"></i> 查看完整API文档
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4">
        <div class="container text-center">
            <p class="mb-0">&copy; 2024 厦门室分监测系统. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/bootstrap.bundle.min.js"></script>
</body>
</html>