<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备服务器API测试</title>
    <link href="js/bootstrap.min.css" rel="stylesheet">
    <style>
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">设备服务器API测试</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>GET /deviceServer/query</h5>
                        <small class="text-muted">查询设备服务器列表</small>
                    </div>
                    <div class="card-body">
                        <form id="queryForm">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">页码 (可选):</label>
                                    <input type="number" class="form-control" id="pageNo" name="pageNo" 
                                           placeholder="默认: 1" min="1">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">每页条数 (可选):</label>
                                    <input type="number" class="form-control" id="pageSize" name="pageSize" 
                                           placeholder="默认: 1000" min="1">
                                </div>
                            </div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-primary" onclick="testServerQuery()">测试查询接口</button>
                            </div>
                        </form>
                        
                        <div class="mt-3">
                            <label class="form-label">响应结果:</label>
                            <div id="queryResult" class="code-block">点击按钮测试接口...</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>POST /deviceServer/add/to/zhwg</h5>
                        <small class="text-muted">同步服务器到综合网管</small>
                    </div>
                    <div class="card-body">
                        <form id="syncForm">
                            <div class="mb-3">
                                <label class="form-label">设备ID:</label>
                                <input type="text" class="form-control" id="deviceId" name="deviceId" 
                                       placeholder="请输入设备ID" required>
                                <div class="form-text">请先通过查询接口获取有效的设备ID</div>
                            </div>
                            <button type="button" class="btn btn-success" onclick="testServerSync()">测试同步接口</button>
                        </form>
                        
                        <div class="mt-3">
                            <label class="form-label">响应结果:</label>
                            <div id="syncResult" class="code-block">点击按钮测试接口...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 服务器列表展示 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>服务器列表展示</h5>
            </div>
            <div class="card-body">
                <div id="serverListDisplay">
                    <div class="text-center text-muted">
                        <p>点击"测试查询接口"按钮加载服务器数据</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>接口说明</h5>
            </div>
            <div class="card-body">
                <h6>1. 服务器查询接口</h6>
                <ul>
                    <li><strong>URL:</strong> GET /deviceServer/query</li>
                    <li><strong>参数:</strong> 
                        <ul>
                            <li><code>pageNo</code> (可选) - 页码，默认1</li>
                            <li><code>pageSize</code> (可选) - 每页条数，默认1000</li>
                        </ul>
                    </li>
                    <li><strong>功能:</strong> 查询室分网管服务器设备列表</li>
                    <li><strong>返回:</strong> JsonResult格式，包含服务器列表数据</li>
                </ul>
                
                <h6>2. 服务器同步接口</h6>
                <ul>
                    <li><strong>URL:</strong> POST /deviceServer/add/to/zhwg</li>
                    <li><strong>参数:</strong> 
                        <ul>
                            <li><code>deviceId</code> - 设备ID (必填)</li>
                        </ul>
                    </li>
                    <li><strong>功能:</strong> 将指定服务器同步到综合网管系统</li>
                    <li><strong>返回:</strong> JsonResult格式，包含同步结果</li>
                </ul>
                
                <h6>3. 服务器字段说明</h6>
                <ul>
                    <li><code>deviceId</code> - 设备ID</li>
                    <li><code>deviceName</code> - 设备名称</li>
                    <li><code>deviceSN</code> - 设备序列号</li>
                    <li><code>category</code> - 设备类别</li>
                    <li><code>model</code> - 设备型号</li>
                    <li><code>manufacturer</code> - 制造商</li>
                    <li><code>line</code> - 线路</li>
                    <li><code>station</code> - 站点</li>
                    <li><code>online</code> - 在线状态 (1=在线, 2=离线, 3=待机, 4=关机, 5=未知)</li>
                    <li><code>workMode</code> - 工作模式 (1=主用, 2=备用)</li>
                    <li><code>ip</code> - IP地址列表</li>
                    <li><code>engineRoom</code> - 机房号</li>
                    <li><code>cabinet</code> - 机柜号</li>
                    <li><code>frame</code> - 机架号</li>
                </ul>
                
                <h6>4. 响应格式</h6>
                <pre class="code-block">{
  "retCode": 0,
  "retMessage": "设备列表响应成功",
  "result": {
    "dataList": [...],    // 服务器设备数组
    "pageNo": 1,          // 当前页码
    "pageSize": 1000,     // 每页大小
    "total": 5            // 总记录数
  }
}</pre>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script>
        // 测试服务器查询接口
        async function testServerQuery() {
            const resultDiv = document.getElementById('queryResult');
            const pageNo = document.getElementById('pageNo').value;
            const pageSize = document.getElementById('pageSize').value;
            
            resultDiv.textContent = '正在请求...';
            resultDiv.style.color = '#000';
            
            try {
                let url = '/ims/deviceServer/query';
                const params = new URLSearchParams();
                
                if (pageNo) params.append('pageNo', pageNo);
                if (pageSize) params.append('pageSize', pageSize);
                
                if (params.toString()) {
                    url += '?' + params.toString();
                }
                
                const response = await fetch(url);
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (result.retCode === 0) {
                    resultDiv.style.color = '#28a745';
                    displayServerList(result.result.dataList || []);
                } else {
                    resultDiv.style.color = '#dc3545';
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
                resultDiv.style.color = '#dc3545';
            }
        }
        
        // 测试服务器同步接口
        async function testServerSync() {
            const resultDiv = document.getElementById('syncResult');
            const deviceId = document.getElementById('deviceId').value;
            
            if (!deviceId) {
                resultDiv.textContent = '请输入设备ID';
                resultDiv.style.color = '#dc3545';
                return;
            }
            
            resultDiv.textContent = '正在请求...';
            resultDiv.style.color = '#000';
            
            try {
                const formData = new FormData();
                formData.append('deviceId', deviceId);
                
                const response = await fetch('/ims/deviceServer/update/to/zhwg', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (result.retCode === 0) {
                    resultDiv.style.color = '#28a745';
                } else {
                    resultDiv.style.color = '#dc3545';
                }
            } catch (error) {
                resultDiv.textContent = '请求失败: ' + error.message;
                resultDiv.style.color = '#dc3545';
            }
        }
        
        // 显示服务器列表
        function displayServerList(servers) {
            const displayDiv = document.getElementById('serverListDisplay');
            
            if (!servers || servers.length === 0) {
                displayDiv.innerHTML = '<div class="text-center text-muted"><p>暂无服务器数据</p></div>';
                return;
            }
            
            let html = `
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>设备ID</th>
                                <th>设备名称</th>
                                <th>设备SN</th>
                                <th>型号</th>
                                <th>线路</th>
                                <th>站点</th>
                                <th>状态</th>
                                <th>工作模式</th>
                                <th>IP地址</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            servers.forEach(server => {
                html += `
                    <tr>
                        <td><code>${server.deviceId || '-'}</code></td>
                        <td>${server.deviceName || '-'}</td>
                        <td>${server.deviceSN || '-'}</td>
                        <td>${server.model || '-'}</td>
                        <td>${server.line || '-'}</td>
                        <td>${server.station || '-'}</td>
                        <td>${getStatusBadge(server.online)}</td>
                        <td>${getWorkModeBadge(server.workMode)}</td>
                        <td>${getIpDisplay(server.ip)}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="fillDeviceId('${server.deviceId}')">
                                选择
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
            `;
            
            displayDiv.innerHTML = html;
        }
        
        // 获取状态徽章
        function getStatusBadge(status) {
            const statusMap = {
                1: { text: '在线', class: 'success' },
                2: { text: '离线', class: 'danger' },
                3: { text: '待机', class: 'warning' },
                4: { text: '关机', class: 'secondary' },
                5: { text: '未知', class: 'info' }
            };
            const statusInfo = statusMap[status] || { text: '未知', class: 'secondary' };
            return `<span class="badge bg-${statusInfo.class}">${statusInfo.text}</span>`;
        }
        
        // 获取工作模式徽章
        function getWorkModeBadge(workMode) {
            switch(workMode) {
                case 1:
                    return '<span class="badge bg-primary">主用</span>';
                case 2:
                    return '<span class="badge bg-secondary">备用</span>';
                default:
                    return '<span class="badge bg-light text-dark">未知</span>';
            }
        }
        
        // 获取IP地址显示
        function getIpDisplay(ipList) {
            if (!ipList || ipList.length === 0) {
                return '-';
            }
            return ipList.join('<br>');
        }
        
        // 填充设备ID到同步表单
        function fillDeviceId(deviceId) {
            document.getElementById('deviceId').value = deviceId;
        }
        
        // 页面加载完成后自动测试查询接口
        document.addEventListener('DOMContentLoaded', function() {
            testServerQuery();
        });
    </script>
</body>
</html>
