package com.key.win.ztrs.dbapi;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.ztrs.bean.Device;
import com.key.win.ztrs.bean.DeviceCell;
import com.key.win.ztrs.bean.DeviceStatus;
import com.key.win.ztrs.service.IDeviceService;
import com.key.win.ztrs.support.GConfig;
import com.key.win.ztrs.support.SFData;
import com.key.win.ztrs.utils.*;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/db/device/*")
public class DeviceDbApi {

  @Resource private IDeviceService deviceService;

  @GetMapping("/query")
  @ApiOperation(value = "query", tags = "[内部]设备数据列表")
  public JsonResult query() {
    LambdaQueryWrapper<Device> lqw = new LambdaQueryWrapper<>();
    lqw.isNotNull(Device::getDeviceSN);
    List<Device> deviceList = deviceService.list(lqw);
    return JsonResult.ok("ok", deviceList);
  }

  /** 由室分网管 -> 综合网管 > 上报设备变化信息 */
  @PostMapping("/info/changeReport")
  @ApiOperation(value = "deviceInfoChangeReport", tags = "设备信息改变由此API上报给综合网管")
  public JsonResult update(@RequestBody Device dev) {
    if (dev == null) {
      return JsonResult.fail("提交数据为空", null, CodeEnum.ERROR_DATA_SUBMIT_NOT_EXIST_20002);
    }
    dev.setSystem(GConfig.clientId);
    dev.setLine(GConfig.line);
    String deviceId = dev.getDeviceId();
    String deviceSN = dev.getDeviceSN();
    Integer opType = dev.getOpType();
    if (opType == null) {
      return JsonResult.fail("opType不能为空", null, CodeEnum.ERROR_OP_ISNULL_10001);
    }
    if (opType > 3 || opType < 0) {
      return JsonResult.fail("opType类型错误", null, CodeEnum.ERROR_OP_UNKNOWN_10002);
    }

    LambdaQueryWrapper<Device> lqw = new LambdaQueryWrapper<>();
    lqw.eq(Device::getDeviceId, deviceId);
    Device dbDevice = deviceService.getOne(lqw, true);
    if (opType != 1 && dbDevice == null) {
      return JsonResult.fail("操作数据不存在", null, CodeEnum.ERROR_DATA_NOT_DB_NOT_EXIST_20001);
    }

    DeviceCell dc = new DeviceCell();
    dc.setSlot("01");
    dc.setPort("01");
    dc.setModuleName("RFID");
    List<DeviceCell> cells = new ArrayList<>();
    cells.add(dc);

    // 1表示增加，2表示删除，3表示修改
    dev.setOpType(opType);

    List<Device> list = new ArrayList<>();
    if (opType == 1) {
      Device newDevice = new Device();
      SpringBeanUtilsExt.copyProperties(dev, newDevice);
      newDevice.setDeviceIp(dev.getIp().get(0));
      newDevice.setCellList(cells);
      newDevice.setOtherList(new ArrayList<>());
      newDevice.setSystem(GConfig.clientId);
      newDevice.setLine(GConfig.line);
      deviceService.save(newDevice);

      list.add(newDevice);
    } else if (opType == 2) {
      deviceService.removeById(dbDevice.getDeviceId());
      dbDevice.setCellList(cells);
      dbDevice.setOpType(opType);
      list.add(dbDevice);
    } else if (opType == 3) {
      SpringBeanUtilsExt.copyProperties(dev, dbDevice);
      dbDevice.setOpType(opType);
      dbDevice.setCellList(cells);
      dbDevice.setDeviceIp(dbDevice.getIp().get(0));
      deviceService.updateById(dbDevice);
      list.add(dbDevice);
    }
    SFData.report("infoChangeReport", list);
    if (opType != 2) {
      statusChangeReport(dev);
    }
    return JsonResult.ok("已向综合网管发送[" + opType + "]请求", list);
  }

  @PostMapping("/status/changeReport")
  @ApiOperation(value = "deviceStatusChangeReport", tags = "设备状态改变 由此API上报给综合网管")
  private void statusChangeReport(@RequestBody Device dev) {
    List<DeviceStatus> dataList = new ArrayList<>();
    DeviceStatus ds = new DeviceStatus();
    ds.setDeviceId(dev.getDeviceId());
    ds.setStatus(String.valueOf(dev.getOnline()));
    ds.setLine(dev.getLine());
    ds.setSystem(dev.getSystem());
    ds.setReportTime(DateTime.now().toString(DatePattern.NORM_DATETIME_MS_PATTERN));
    ds.setIp(dev.getIp().get(0));
    dataList.add(ds);
    SFData.report("statusChangeReport", dataList);
  }

  /** 首次导入点表之后 ，需要调用该方法用于填充设备名称 */
  @GetMapping("/update")
  @ApiOperation(value = "update", tags = "[综合网管]更新设备名称为设备描述名称")
  public Result update() {

    LambdaQueryWrapper<Device> lqw = new LambdaQueryWrapper<>();
    lqw.ne(Device::getDeviceTag, "00000000");
    List<Device> deviceList = deviceService.list(lqw);

    for (Device d : deviceList) {
      String stationId = d.getStation();
      String stationName = SFData.getStations(GConfig.line).get(stationId);
      if (d.getCategory().contains("监控主机")) {
        d.setDeviceName(stationName + "监控主机" + d.getDeviceTag());
      } else if (d.getCategory().contains("室分天线")) {
        d.setDeviceName(stationName + d.getHostNumber() + "室分天线" + d.getDeviceTag());
      }
    }

    deviceService.updateBatchById(deviceList);
    log.info("已更新数据表....>");
    return Result.succeed(200, "更新完成");
  }

  @GetMapping("/status/changeReport2")
  @ApiOperation(value = "deviceStatusChangeReport2", tags = "设备状态改变 由此API上报给综合网管,该API由室分程序内部调用")
  private void statusChangeReport2(
      @RequestParam String hostNumber, @RequestParam String deviceStatus) {

    LambdaQueryWrapper<Device> lqw = new LambdaQueryWrapper<>();
    lqw.eq(Device::getDeviceSN, hostNumber);
    List<Device> list = deviceService.list(lqw);
    if (list.size() > 0) {
      Device dev = list.get(0);
      List<DeviceStatus> dataList = new ArrayList<>();
      DeviceStatus ds = new DeviceStatus();
      ds.setDeviceId(dev.getDeviceId());
      ds.setStatus(String.valueOf(deviceStatus));

      ds.setLine(dev.getLine());
      ds.setSystem(dev.getSystem());
      ds.setReportTime(DateTime.now().toString(DatePattern.NORM_DATETIME_MS_PATTERN));
      ds.setIp(dev.getIp().get(0));
      dataList.add(ds);
      SFData.report("statusChangeReport", dataList);

      dev.setOpType(3); // 3修改  1新增  2删除
      // 在线状态。1表示在线，2表示离线，3表示待机，4表示关机，5表示未知
      dev.setOnline(Integer.parseInt(deviceStatus));
      deviceService.updateById(dev);
    }
  }
}
