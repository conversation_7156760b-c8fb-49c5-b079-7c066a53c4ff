package com.key.win.ztrs.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.key.win.ztrs.bean.DeviceServer;
import com.key.win.ztrs.dao.DeviceServerDao;
import com.key.win.ztrs.service.IDeviceServerService;
import org.springframework.stereotype.Service;

@Service
public class DeviceServerServiceImpl extends ServiceImpl<DeviceServerDao, DeviceServer>
    implements IDeviceServerService {}
