package com.key.win.ztrs.init;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.key.win.ztrs.bean.Alarm;
import com.key.win.ztrs.bean.AlarmCode;
import com.key.win.ztrs.bean.Device;
import com.key.win.ztrs.bean.DeviceCell;
import com.key.win.ztrs.support.GConfig;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MockDataBean {

  public static List<Alarm> alarmDataList = new ArrayList<>();

  public static List<AlarmCode> alarmCodeDataList = new ArrayList<>();

  public static List<Device> deviceDataList = new ArrayList<>();

  public static void initData() {
    mockAlarmCodeData();
  }


  /** 告警码数据Mock */
  private static void mockAlarmCodeData() {
    AlarmCode ac1 = new AlarmCode();
    ac1.setLine(GConfig.line);
    ac1.setSystem(GConfig.clientId);
    ac1.setCode("1");
    ac1.setTitle("设备离线");
    ac1.setText("设备与网管MQ通讯失败");
    ac1.setCause("MQ节点宕机或者网络出现故障");
    ac1.setLevel(2);
    ac1.setSuggestion("检查MQ节点是否正常,检查网络通讯是否正常");

    AlarmCode ac2 = new AlarmCode();
    ac2.setLine(GConfig.line);
    ac2.setSystem(GConfig.clientId);
    ac2.setCode("2");
    ac2.setTitle("天线异常");
    ac2.setText("天线异常");
    ac2.setCause("馈线松动");
    ac2.setLevel(3);
    ac2.setSuggestion("检查天线馈线连接是否松动");

    alarmCodeDataList.add(ac1);
    alarmCodeDataList.add(ac2);
  }
}
