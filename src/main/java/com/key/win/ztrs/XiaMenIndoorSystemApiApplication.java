package com.key.win.ztrs;

import com.key.win.ztrs.support.GConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Slf4j
@EnableSwagger2
@EnableScheduling
@ConfigurationProperties(prefix = "api")
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class XiaMenIndoorSystemApiApplication {

  public static void main(String[] args) {
    SpringApplication.run(XiaMenIndoorSystemApiApplication.class, args);
    log.info("<{}> <综合网管告警上报接口服务启动成功....>", GConfig.line);
  }
}
