package com.key.win.ztrs;

import com.key.win.ztrs.init.MockDataBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Slf4j
@EnableSwagger2
@EnableScheduling
@ConfigurationProperties(prefix = "api")
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class XiaMenIndoorSystemApiApplication {

  public static void main(String[] args) {
    SpringApplication.run(XiaMenIndoorSystemApiApplication.class, args);
    log.info("###数据初始化开始>>>>>>");
    MockDataBean.initData();
    log.info("###数据初始化结束!!!!!!");
    log.info("<4号线接口服务启动....>");
  }
}
