package com.key.win.ztrs.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

@Configuration
public class SwaggerConfig {

  Boolean swaggerEnabled = true;

  @Bean
  public Docket createRestApi() {
    return new Docket(DocumentationType.OAS_30)
        .apiInfo(apiInfo())
        // 是否开启
        .enable(swaggerEnabled)
        .select()
        // 指定接口的位置
        .apis(RequestHandlerSelectors.basePackage("com.key.win.ztrs.api"))
        .paths(PathSelectors.any())
        .build();
  }

  /**
   * 配置网站的基本信息
   *
   * @return
   */
  @Bean
  public ApiInfo apiInfo() {
    return new ApiInfoBuilder()
        // 网站标题
        .title("综合网管-服务接口文档")
        // 联系人信息
        .contact(new Contact("tivy", "", "<EMAIL>"))
        // 版本号
        .version("1.0.0")
        // 描述
        .description("厦门综合网管接口文档")
        .build();
  }
}
