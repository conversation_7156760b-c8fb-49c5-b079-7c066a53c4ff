package com.key.win.ztrs.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import lombok.Data;

@Data
@Entity(name = "zhwg_alarm_code")
@TableName("zhwg_alarm_code")
public class AlarmCode implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableField(exist = false)
  @Transient
  private String line;

  @TableField(exist = false)
  @Transient
  private String system;

  @TableId @Id
  private String code;

  private String title;
  private String titleTranslation;

  private int level;

  @JsonIgnore private String description;

  @Transient
  @TableField(exist = false)
  private String text;

  private String textTranslation;
  private String cause;
  private String suggestion;

  /** 信息操作类型，1表示增加，2表示删除，3表示修改。主动查询不需要携带，变更信息上报具备 */
  private Integer opType;

  @TableField(exist = false)
  @Transient
  private List<Map<String, String>> otherList;
}
