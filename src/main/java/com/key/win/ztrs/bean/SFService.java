package com.key.win.ztrs.bean;

import java.io.Serializable;
import lombok.Data;

@Data
public class SFService implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 服务模块唯一标识 */
  private String serviceId;

  /** 服务模块名称 */
  private String serviceName;

  /** 服务所在机器IP */
  private String ip;

  private int port;

  /** 服务进程号 */
  private int pid;

  /** 服务模块用途 */
  private String serviceDesc;

  /** 服务模块占用CPU使用率 */
  private String cpuUsage;

  /** 服务模块占用内存使用率 */
  private String memoryUsage;

  /** 上报时间 */
  private String reportTime;
}
