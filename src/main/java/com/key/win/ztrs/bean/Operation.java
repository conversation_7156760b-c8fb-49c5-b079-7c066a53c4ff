package com.key.win.ztrs.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 操作记录对象定义
 */
@Data
public class Operation implements Serializable {

  private static final long serialVersionUID = 1L;

  /** 操作记录ID，唯一确定一条操作记录 */
  private String operationId;

  private String line;
  private String system;

  /** 来源ip */
  private String ip;

  /** 操作时间 */
  private String time;

  /** 操作用户 */
  private String operator;

  /** 操作类型 */
  private String type;

  /** 操作内容 */
  private String content;

  /** 操作结果。1表示成功；2表示失败；3表示未知 */
  private int result;

  /** 操作描述信息 */
  private String description;

  /** 其他需要补充的信息，以键值对方式提供 */
  private List<Map> otherList;
}
