package com.key.win.ztrs.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class DeviceCell implements Serializable {

  private static final long serialVersionUID = 1L;

  private String subFrame;
  private String slot;
  private String port;
  private String moduleName;
  private String sn;
  private String category;
  private String model;
  private String manufacturer;
  private String serialnum;
  private String version;
  private Integer mode;
  private String description;
  private List<Map<String, String>> otherList;
}
