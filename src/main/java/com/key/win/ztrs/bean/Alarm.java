package com.key.win.ztrs.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import javax.persistence.Transient;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
@TableName("zhwg_alarm_vo")
public class Alarm implements Serializable {

  @TableId
  private String alarmId;

  @Transient
  @TableField(exist = false)
  @JsonIgnore
  private String stationCode;

  private String stationId;

  @Transient
  @TableField(exist = false)
  private String line;

  @Transient
  @TableField(exist = false)
  private String system;

  private String deviceId;

  @Transient
  @TableField(exist = false)
  private String subFrame = "";

  @Transient
  @TableField(exist = false)
  private String slot = "";

  @Transient
  @TableField(exist = false)
  private String port ="18080";

  /** 告警状态。1表示告警产生，未处理状态 2表示告警恢复 3告警确认 */
  private Integer status;

  @JsonIgnore
  private Integer isReport;

  /**
   * 告警编码
   */
  @Transient
  @TableField(exist = false)
  private String code;

  /**
   * 告警发生时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS",timezone = "GMT+8")
  private Date alarmTime;

  /**
   * 告警恢复时间。如果是告警恢复消息必须具备，告警上报消息不需要携带。
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS",timezone = "GMT+8")
  private Date recoveryTime;

  @Transient
  @TableField(exist = false)
  private List<Map<String, String>> otherList;

  @JsonIgnore
  private String alarmType;
}
