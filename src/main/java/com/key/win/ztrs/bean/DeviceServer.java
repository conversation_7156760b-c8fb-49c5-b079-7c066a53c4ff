package com.key.win.ztrs.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.key.win.ztrs.support.GConfig;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
@TableName("zhwg_device_server")
@Entity(name = "zhwg_device_server")
public class DeviceServer implements Serializable {
  private static final long serialVersionUID = 1L;

  @Id @TableId private String deviceId;

  private String line;
  private String station;

  @TableField(exist = false)
  @Transient
  private String system = GConfig.clientId;

  /** 机房号码 */
  private String engineRoom = "0";

  /** 机柜号 */
  private String cabinet = "0";

  /** 机架号 */
  private String frame;

  private String deviceName;
  @JsonIgnore private String deviceTag;
  private String category;
  private String model;
  private String manufacturer;
  private String size;

  @TableField(value = "device_sn")
  private String deviceSN;

  private String deviceVersion;

  @Transient
  @TableField(exist = false)
  private List<String> ip;

  @JsonIgnore
  @TableField(value = "ip")
  private String deviceIp;

  private String longitude;
  private String latitude;
  private String altitude;

  /** 在线状态。1表示在线，2表示离线，3表示待机，4表示关机，5表示未知 */
  private int online;

  /** 工作模式。1表示主用，2表示备用 */
  private Integer workMode;

  private String description;

  @Transient
  @TableField(exist = false)
  private List<DeviceCell> cellList = new ArrayList<>();

  /** 信息操作类型，1表示增加，2表示删除，3表示修改。 主动查询不需要携带，变更信息上报具备 */
  private Integer opType;

  @JsonIgnore private String hostNumber;

  @Transient
  @TableField(exist = false)
  private List<Map<String, String>> otherList = new ArrayList<>();

  public List<String> getIp() {
    if (ip == null || ip.isEmpty()) {
      boolean bl = StringUtils.isBlank(deviceIp);
      if (bl) {
        return new ArrayList<>();
      } else {
        ArrayList<String> ipList = new ArrayList<>();
        ipList.add(deviceIp);
        return ipList;
      }
    } else {
      return ip;
    }
  }

  public void setIp(List<String> ip) {
    if (ip == null || ip.isEmpty()) {
      ip = new ArrayList<>();
      if (StringUtils.isNotBlank(deviceIp)) {
        ip.add(this.deviceIp);
      }
    }
    this.ip = ip;
  }
}
