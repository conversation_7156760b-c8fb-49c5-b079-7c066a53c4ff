package com.key.win.ztrs.api;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.ztrs.bean.Device;
import com.key.win.ztrs.bean.DeviceCell;
import com.key.win.ztrs.service.IDeviceService;
import com.key.win.ztrs.support.SFData;
import com.key.win.ztrs.utils.JsonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.*;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/device/*")
@Api(value = "deviceAPI", tags = "设备列表API")
public class DeviceApi {

  @Resource private IDeviceService deviceService;

  /** 设备查询接口 */
  @GetMapping("/query")
  @ApiOperation(value = "query", tags = "[综合网管]设备数据列表，综合网管主动调用")
  public JsonResult query(
      @RequestParam(required = false) Integer pageNo,
      @RequestParam(required = false) Integer pageSize) {

    if (pageNo == null) {
      pageNo = 1;
    }
    if (pageSize == null) {
      pageSize = 1000;
    }

    DeviceCell dc = new DeviceCell();
    dc.setSlot("01");
    dc.setPort("01");
    dc.setModuleName("RFID");
    List<DeviceCell> cells = new ArrayList<>();
    cells.add(dc);

    LambdaQueryWrapper<Device> lqw = new LambdaQueryWrapper<>();
    lqw.isNotNull(Device::getDeviceSN);
    List<Device> deviceList = deviceService.list(lqw);

    List<Map<String, String>> otherList = new ArrayList<>();

    for (Device d : deviceList) {
      String stationId = d.getStation();
      String stationName = SFData.getStations().get(stationId);
      if (d.getCategory().contains("监控主机")) {
        d.setDeviceName(stationName + "监控主机" + d.getDeviceSN());
      } else if (d.getCategory().contains("室分天线")) {
        d.setDeviceName(stationName + d.getHostNumber() + "室分天线" + d.getDeviceName());
      }
      d.setCellList(cells);
      d.setOtherList(otherList);
    }

    Map<String, Object> dataResult = new HashMap<>();
    dataResult.put("dataList", deviceList);
    dataResult.put("pageNo", pageNo);
    dataResult.put("pageSize", pageSize);
    dataResult.put("total", deviceList.size());

    log.info("<[综合网管]设备查询接口>:{}", JSON.toJSONString(deviceList));
    return JsonResult.ok("设备列表响应成功", dataResult);
  }

  @GetMapping("/update")
  @ApiOperation(value = "update", tags = "[综合网管]更新设备名称为设备描述名称")
  public void update() {

    LambdaQueryWrapper<Device> lqw = new LambdaQueryWrapper<>();
    lqw.ne(Device::getDeviceTag, "00000000");
    List<Device> deviceList = deviceService.list(lqw);

    for (Device d : deviceList) {
      String stationId = d.getStation();
      String stationName = SFData.getStations().get(stationId);
      if (d.getCategory().contains("监控主机")) {
        d.setDeviceName(stationName + "监控主机" + d.getDeviceTag());
      } else if (d.getCategory().contains("室分天线")) {
        d.setDeviceName(stationName + d.getHostNumber() + "室分天线" + d.getDeviceTag());
      }
    }

    deviceService.updateBatchById(deviceList);
    log.info("已更新数据表....>");
  }
}
