package com.key.win.ztrs.api;

import com.key.win.ztrs.support.SFData;
import com.key.win.ztrs.utils.JsonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/station/*")
@Api(value = "StationAPI", tags = "站点信息API")
public class StationApi {

    /**
     * 获取站点映射数据
     */
    @GetMapping("/mapping")
    @ApiOperation(value = "getStationMapping", tags = "获取站点映射数据")
    public JsonResult getStationMapping(@RequestParam(required = false) String lineId) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            if (lineId != null && !lineId.trim().isEmpty()) {
                // 获取指定线路的站点映射
                Map<String, String> stationMap = SFData.getStations(lineId);
                if (stationMap != null) {
                    result.put(lineId, stationMap);
                } else {
                    result.put(lineId, new HashMap<>());
                }
            } else {
                // 获取所有线路的站点映射
                Map<String, String> line400Stations = SFData.getStations("400");
                Map<String, String> line600Stations = SFData.getStations("600");
                
                if (line400Stations != null) {
                    result.put("400", line400Stations);
                }
                if (line600Stations != null) {
                    result.put("600", line600Stations);
                }
            }
            
            log.info("获取站点映射数据成功，lineId: {}, 结果: {}", lineId, result);
            return JsonResult.ok("获取站点映射数据成功", result);
        } catch (Exception e) {
            log.error("获取站点映射数据失败", e);
            return JsonResult.fail("获取站点映射数据失败: " + e.getMessage(), null, 
                com.key.win.ztrs.utils.CodeEnum.ERROR_990010001);
        }
    }

    /**
     * 根据线路ID和站点ID获取站点名称
     */
    @GetMapping("/name")
    @ApiOperation(value = "getStationName", tags = "根据线路ID和站点ID获取站点名称")
    public JsonResult getStationName(@RequestParam String lineId, @RequestParam String stationId) {
        try {
            Map<String, String> stationMap = SFData.getStations(lineId);
            String stationName = null;
            
            if (stationMap != null) {
                stationName = stationMap.get(stationId);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("lineId", lineId);
            result.put("stationId", stationId);
            result.put("stationName", stationName != null ? stationName : "未知站点");
            
            log.info("获取站点名称成功，lineId: {}, stationId: {}, stationName: {}", 
                lineId, stationId, stationName);
            return JsonResult.ok("获取站点名称成功", result);
        } catch (Exception e) {
            log.error("获取站点名称失败，lineId: {}, stationId: {}", lineId, stationId, e);
            return JsonResult.fail("获取站点名称失败: " + e.getMessage(), null, 
                com.key.win.ztrs.utils.CodeEnum.ERROR_990010001);
        }
    }
}
