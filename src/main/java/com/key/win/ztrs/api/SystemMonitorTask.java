package com.key.win.ztrs.api;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson2.JSON;
import com.key.win.ztrs.bean.DeviceMessage;
import com.key.win.ztrs.bean.DeviceServer;
import com.key.win.ztrs.bean.Performance;
import com.key.win.ztrs.service.IDeviceServerService;
import com.key.win.ztrs.support.GConfig;
import com.key.win.ztrs.utils.OKHttpUtils;
import com.key.win.ztrs.utils.SignatureUtils;
import com.key.win.ztrs.utils.SystemMetricsUtil;
import java.time.format.DateTimeFormatter;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SystemMonitorTask {

  private static final DateTimeFormatter formatter =
      DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

  @Value("${api.url}")
  private String zhwgPath;

  private IDeviceServerService deviceServerService;

  @Scheduled(fixedRate = 60 * 5 * 1000) // 每 10 秒执行一次
  public void collectSystemMetrics() {

    DeviceServer deviceServer = GConfig.deviceServer;

    List<DeviceMessage> deviceMessages = new ArrayList<>();
    DeviceMessage msg = new DeviceMessage();
    msg.setSystem(GConfig.clientId);
    msg.setLine(GConfig.line);
    msg.setDeviceId(deviceServer.getDeviceId());

    List<Performance> perfs = new ArrayList<>();

    String now = DateTime.now().toString(DatePattern.NORM_DATETIME_MS_PATTERN);

    Performance cpu = new Performance();
    cpu.setLastTime(now);
    cpu.setUnit("%");
    cpu.setDataName("CPU使用率");
    cpu.setAlarmEnable(0);
    cpu.setTitle("center_cpu_usage");
    cpu.setType(1);
    cpu.setValue(String.format("%.2f", SystemMetricsUtil.getCpuUsage()));
    cpu.setSamplingCycle(60 * 1000);
    perfs.add(cpu);

    Performance mem = new Performance();
    mem.setLastTime(now);
    mem.setUnit("%");
    mem.setDataName("内存使用率");
    mem.setAlarmEnable(0);
    mem.setTitle("center_mem_usage");
    mem.setType(1);
    mem.setValue(String.format("%.2f", SystemMetricsUtil.getMemUsage()));
    mem.setSamplingCycle(60 * 1000);
    perfs.add(mem);

    Performance disk = new Performance();
    disk.setLastTime(now);
    disk.setUnit("%");
    disk.setDataName("磁盘使用率");
    disk.setAlarmEnable(0);
    disk.setTitle("center_disk_usage");
    disk.setType(1);
    disk.setValue(String.format("%.2f", SystemMetricsUtil.getDiskUsage()));
    disk.setSamplingCycle(60 * 1000);
    perfs.add(disk);

    msg.setPerformanceList(perfs);
    deviceMessages.add(msg);
    log.info("设备消息：{}", JSON.toJSONString(msg));

    String requestBaseUrl = zhwgPath + "/performance/update";
    Map<String, String> requestParamsMap = new HashMap<>();
    String requestUrl = SignatureUtils.buildSignatureUrl(requestBaseUrl, requestParamsMap);

    Map<String, Object> bodyMap = new HashMap<>();
    bodyMap.put("clientId", GConfig.clientId);
    bodyMap.put("line", GConfig.line);
    bodyMap.put("rows", deviceMessages.size());
    bodyMap.put("dataList", deviceMessages);
    log.info("性能数据实时上报接口：{}", requestBaseUrl);
    OKHttpUtils.send(requestUrl, bodyMap);
  }
}
