package com.key.win.ztrs.api;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.key.win.ztrs.bean.SFService;
import com.key.win.ztrs.utils.IDUtils;
import com.key.win.ztrs.utils.IPUtils;
import com.key.win.ztrs.utils.OKHttpUtils;
import com.key.win.ztrs.utils.SignatureUtils;
import com.sun.management.OperatingSystemMXBean;
import java.lang.management.ManagementFactory;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationPid;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class JobApi {

  @Value("${api.url}")
  private String zhwgPath;

  @Value("${api.clientId}")
  private String clientId;

  @Value("${api.line}")
  private String line;

  /** 心跳 上报频率：10S */
  @Scheduled(fixedRate = 10 * 1000)
  public void heartBeat() {
    String requestBaseUrl = zhwgPath + "/heartbeat/update";
    Map<String, String> requestParamsMap = new HashMap<>();
    String requestUrl = SignatureUtils.buildSignatureUrl(requestBaseUrl, requestParamsMap);

    Map<String, String> bodyMap = new HashMap<>();
    bodyMap.put("clientId", clientId);
    bodyMap.put("line", line);
    log.info("向综合网管发送心跳：{}", requestBaseUrl);
    OKHttpUtils.send(requestUrl, bodyMap);
  }

  /**
   * 业务服务运行状态实时上报接口 接口方向：通信子系统发起，综合网管系统响应 接口名称：http://接口地址IP:端口/ims/service/status/update
   * 上报方式：上报最小间隔300秒
   */
  @Scheduled(fixedRate = 300 * 1000)
  public void sfServiceStatusReport() {

    String requestBaseUrl = zhwgPath + "/service/status/update";
    Map<String, String> requestParamsMap = new HashMap<>();
    String requestUrl = SignatureUtils.buildSignatureUrl(requestBaseUrl, requestParamsMap);

    String pid = new ApplicationPid().toString();
    OperatingSystemMXBean operatingSystemMXBean =
        (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
    double cpuUsage = operatingSystemMXBean.getSystemCpuLoad() * 100;
    double _cpuUsage = Math.round(cpuUsage);
    double totalPhysicalMemorySize = operatingSystemMXBean.getTotalPhysicalMemorySize();
    double freePhysicalMemorySize = operatingSystemMXBean.getFreePhysicalMemorySize();

    double memUsage = (totalPhysicalMemorySize - freePhysicalMemorySize) / totalPhysicalMemorySize;
    BigDecimal bd = new BigDecimal(memUsage).setScale(2, RoundingMode.HALF_DOWN);
    double _memUsage = bd.doubleValue() * 100;

    List<SFService> dataList = new ArrayList<>();
    SFService s = new SFService();
    s.setServiceId(IDUtils.createId() + "");
    try {
      String siteLocalIp = IPUtils.getSiteLocalIp();
      s.setIp(siteLocalIp);
    } catch (SocketException e) {
      throw new RuntimeException(e);
    }

    s.setPort(18080);
    s.setPid(Integer.parseInt(pid));
    s.setServiceDesc("室分网管程序主服务");
    s.setServiceName("室分网管服务");
    s.setReportTime(DateTime.now().toString(DatePattern.NORM_DATETIME_MS_FORMAT));
    s.setCpuUsage(_cpuUsage + "%");
    s.setMemoryUsage(_memUsage + "%");
    dataList.add(s);

    Map<String, Object> bodyMap = new HashMap<>();
    bodyMap.put("line", line);
    bodyMap.put("system", clientId);
    bodyMap.put("rows", dataList.size());
    bodyMap.put("dataList", dataList);

    OKHttpUtils.send(requestUrl, bodyMap);
  }
}
