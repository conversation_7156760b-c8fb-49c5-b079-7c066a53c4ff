package com.key.win.ztrs.api;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.key.win.ztrs.bean.Operation;
import com.key.win.ztrs.support.GConfig;
import com.key.win.ztrs.support.SFData;
import com.key.win.ztrs.utils.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.net.SocketException;
import java.util.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/operation/*")
@RestController
@Api(value = "OperationAPI", tags = "告警操作API")
public class OperationApi {

  /**
   * @Desc1 操作记录实时上报接口
   *
   * @user 该接口用于通信子系统向综合网管系统上报操作记录，综合网管系统提供接口服务，并反馈数据接收结果。
   * @time 接口方向：通信子系统发起，综合网管系统响应
   */
  @GetMapping("/operationReport")
  @ApiOperation(value = "operationReport", tags = "告警操作变化后上报给综合网管")
  public JsonResult operationReport(
      @RequestParam String type,
      @RequestParam String opUser,
      @RequestParam String content,
      @RequestParam String result)
      throws SocketException {

    List<Operation> dataList = new ArrayList<>();
    Operation op = new Operation();
    op.setOperationId(String.valueOf(IDUtils.createId()));
    op.setLine(GConfig.line);
    op.setSystem(GConfig.clientId);
    op.setIp(IPUtils.getSiteLocalIp());
    op.setTime(DateTime.now().toString(DatePattern.NORM_DATETIME_MS_PATTERN));
    op.setType(StringUtils.defaultString(type, "type"));
    op.setOperator(StringUtils.defaultString(opUser, "管理员"));
    op.setContent(StringUtils.defaultString(content, "告警操作"));
    op.setResult(
        Integer.parseInt(StringUtils.defaultString(result, "1"))); // 操作结果。1表示成功；2表示失败；3表示未知
    op.setDescription(StringUtils.defaultString(content, "告警操作"));
    dataList.add(op);

    SFData.report("operationReport", dataList);
    return JsonResult.ok(CodeEnum.SUCCESS.getCode(), "信息同步成功", dataList);
  }
}
