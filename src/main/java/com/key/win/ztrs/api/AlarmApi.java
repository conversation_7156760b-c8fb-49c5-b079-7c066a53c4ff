package com.key.win.ztrs.api;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.key.win.ztrs.bean.Alarm;
import com.key.win.ztrs.service.IAlarmService;
import com.key.win.ztrs.support.GConfig;
import com.key.win.ztrs.support.SFData;
import com.key.win.ztrs.utils.CodeEnum;
import com.key.win.ztrs.utils.JsonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.*;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/alarm/*")
@Api(value = "AlarmAPI", description = "告警接口")
public class AlarmApi {

  @Resource private IAlarmService alarmService;

  /** 告警记录查询接口 */
  @ApiOperation(value = "query", tags = "[综合网管]由综合网管调用获取告警记录数据")
  @GetMapping("/query")
  public JsonResult query(
      @RequestParam(required = false) String beginTime,
      @RequestParam(required = false) String endTime,
      @RequestParam(required = false) String alarmIds,
      @RequestParam(required = false) Integer alarmStatus,
      @RequestParam(required = false) Integer pageNo,
      @RequestParam(required = false) Integer pageSize) {

    log.info("<alarmIds> : {}", alarmIds);
    log.info("<alarmStatus> : {}", alarmStatus);
    log.info("<pageNo>: {}", pageNo);
    log.info("<pageSize> : {}", pageSize);

    LambdaQueryWrapper<Alarm> lqw = new LambdaQueryWrapper<>();

    DateTime _beginTime = null;
    DateTime _endTime = null;

    if (StrUtil.isNotBlank(beginTime)) {
      _beginTime = DateTime.of(beginTime, DatePattern.NORM_DATETIME_PATTERN);
    } else {
      _beginTime = DateTime.of("2020-01-01 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
    }

    if (StrUtil.isNotBlank(endTime)) {
      _endTime = DateTime.of(endTime, DatePattern.NORM_DATETIME_PATTERN);
    } else {
      _endTime = DateTime.of("2999-01-01 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
    }

    log.info("<beginTime> : {},{}", beginTime, _beginTime);
    log.info("<endTime> : {},{}", endTime, _endTime);
    lqw.between(Alarm::getAlarmTime, _beginTime, _endTime);

    if (alarmStatus == null) {
      alarmStatus = 1;
    }
    lqw.eq(Alarm::getStatus, alarmStatus);

    if (pageNo == null) {
      pageNo = 1;
    }
    if (pageSize == null) {
      pageSize = 1000;
    }

    if (alarmIds != null) {
      String[] ids = alarmIds.split(",");
      lqw.in(Alarm::getAlarmId, ids);
    }

    IPage<Alarm> page = new Page();
    page.setCurrent(pageNo);
    page.setSize(pageSize);

    IPage<Alarm> dataPage = alarmService.page(page, lqw);
    for (Alarm al : dataPage.getRecords()) {
      al.setLine(GConfig.line);
      al.setSystem(GConfig.clientId);


      if(al.getAlarmType().equals("固定主机")){
          al.setCode("1");
      }else if(al.getAlarmType().equals("天线")){
          al.setCode("3");
      }

      String time = DateTime.of(al.getAlarmTime()).toString(DatePattern.NORM_DATETIME_MS_PATTERN);
      DateTime dateTime = DateTime.of(time, DatePattern.NORM_DATETIME_MS_PATTERN);
      al.setAlarmTime(dateTime);
    }
    log.info("<[综合网管]告警记录查询接口>:{}", JSON.toJSONString(dataPage.getRecords()));

    Map<String, Object> dataResult = new HashMap<>();
    dataResult.put("dataList", dataPage.getRecords());
    dataResult.put("pageNo", pageNo);
    dataResult.put("pageSize", pageSize);
    dataResult.put("total", dataPage.getRecords().size());

    return JsonResult.ok("列表响应成功", dataResult);
  }

  /**
   * 该接口用于通信子系统向综合网管系统上报告警信息，综合网管系统提供接口服务，并反馈数据接收结果。
   * 接口方向：通信子系统发起，综合网管系统响应
   * */
  @GetMapping("/alarmReport")
  @ApiOperation(value = "alarmReport", tags = "告警信息上报")
  public JsonResult alarmReport(@RequestParam String operation, @RequestParam String alarmIds) {
    if (StringUtils.isBlank(operation)) {
      return JsonResult.fail(
          "操作CODE不存在", null, CodeEnum.ERROR_DATA_OPERATION_CODE_NOT_EXIST_400001);
    }
    // 告警操作。1表示手动确认，2表示手动恢复 3表示手动反确认
    if (!(StringUtils.equals(operation, "1")
        || StringUtils.equals(operation, "2")
        || StringUtils.equals(operation, "3"))) {
      return JsonResult.fail("操作CODE不存在", null, CodeEnum.ERROR_DATA_OPERATION_CODE_UNKNOWN_400002);
    }

    Integer _operation = Integer.valueOf(operation);

    LambdaQueryWrapper<Alarm> lqw = new LambdaQueryWrapper<>();

    if (StringUtils.isNotBlank(alarmIds)) {
      String[] ids = alarmIds.split(",");
      lqw.in(Alarm::getAlarmId, ids);
    } else {
      lqw.eq(Alarm::getIsReport, 0);
    }
    List<Alarm> prepareReportAlarmList = alarmService.list(lqw);
    for (Alarm alarm : prepareReportAlarmList) {
      alarm.setLine(GConfig.line);
      alarm.setSystem(GConfig.clientId);
      if(alarm.getAlarmType().contains("固定主机")){
          alarm.setCode("1");
      }else if (alarm.getAlarmType().contains("天线")){
          alarm.setCode("3");
      }
      // 告警状态。1表示告警产生，未处理状态 2表示告警恢复 3告警确认
      alarm.setStatus(_operation);
      if (_operation == 2) {
        if (alarm.getRecoveryTime() == null) {
          alarm.setRecoveryTime(DateTime.now().setTimeZone(TimeZone.getTimeZone("GMT+8")));
        }
      }
    }
    SFData.report("alarmReport", prepareReportAlarmList);
    return JsonResult.ok("告警信息上报成功", prepareReportAlarmList);
  }

  /**
   * 由综合网管调用该API
   *
   * @param param 综合网管操作的告警信息
   * @return 室分网管确认的告警列表ID
   */
  @PostMapping("/operation")
  @ApiOperation(value = "operation", tags = "[综合网管]告警操作")
  public JsonResult alarmOperation(@RequestBody Map<String, Object> param) {
    log.info("<来自[综合网管系统]的告警操作> : {}", param);
    ArrayList alarmIds = MapUtil.get(param, "alarmIds", ArrayList.class);
    String operation = MapUtil.get(param, "operation", String.class);
    log.info("<来自[综合网管系统]的告警操作 ids> {} ,操作码 : {}", alarmIds, operation);
    return JsonResult.ok("列表响应成功", alarmIds);
  }

  /**
   * 由室分网管主动上报告警操作
   *
   * @return
   */
  @GetMapping("/alarmOperationChangeReport")
  @ApiOperation(value = "alarmOperationChangeReport", tags = "告警操作主动上报给综合网管")
  public JsonResult alarmOperationReport(
      @RequestParam String operation, @RequestParam String alarmIds) {

    if (StringUtils.isBlank(operation)) {
      return JsonResult.fail(
          "操作CODE不存在", null, CodeEnum.ERROR_DATA_OPERATION_CODE_NOT_EXIST_400001);
    }
    // 告警操作。1表示手动确认，2表示手动恢复 3表示手动反确认

    if (!(StringUtils.equals(operation, "1")
        || StringUtils.equals(operation, "2")
        || StringUtils.equals(operation, "3"))) {
      return JsonResult.fail("操作CODE不存在", null, CodeEnum.ERROR_DATA_OPERATION_CODE_UNKNOWN_400002);
    }

    LambdaQueryWrapper<Alarm> lqw = new LambdaQueryWrapper<>();
    if (StringUtils.isNotBlank(alarmIds)) {
      String[] operationAlarmIds = alarmIds.split(",");
      lqw.in(Alarm::getAlarmId, operationAlarmIds);
    }
    List<Alarm> alarmList = alarmService.list(lqw);

    String[] _ids = new String[alarmList.size()];
    for (int i = 0; i < alarmList.size(); i++) {
      _ids[i] = alarmList.get(i).getAlarmId();
    }
    String reportAlarmIds = String.join(",", _ids);

    Map<String, Object> bodyMap = new HashMap<>();
    bodyMap.put("time", DateTime.now().toString(DatePattern.NORM_DATETIME_MS_PATTERN));
    bodyMap.put("line", GConfig.line);
    bodyMap.put("system", GConfig.clientId);
    bodyMap.put("alarmIds", reportAlarmIds);
    bodyMap.put("operation", operation); // 告警操作。1表示手动确认，2表示手动恢复 3表示手动反确认
    bodyMap.put("description", SFData.operationCode2Desc(operation));

    SFData.reportMap("alarmOperationChangeReport", bodyMap);
    return JsonResult.ok(SFData.operationCode2Desc(operation) + "成功", bodyMap);
  }
}
