package com.key.win.ztrs.api;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.ztrs.bean.DeviceCell;
import com.key.win.ztrs.bean.DeviceServer;
import com.key.win.ztrs.service.IDeviceServerService;
import com.key.win.ztrs.support.GConfig;
import com.key.win.ztrs.support.SFData;
import com.key.win.ztrs.utils.CodeEnum;
import com.key.win.ztrs.utils.JsonResult;
import io.swagger.annotations.Api;
import java.util.*;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/deviceServer/*")
@Api(value = "deviceAPI", tags = "室分网管服务器列表API")
public class DeviceServerApi {

  @Resource private IDeviceServerService deviceServerService;

  /** 设备服务器列表查询接口 */
  @GetMapping("/query")
  public JsonResult query(
      @RequestParam(required = false) Integer pageNo,
      @RequestParam(required = false) Integer pageSize) {

    if (pageNo == null) {
      pageNo = 1;
    }
    if (pageSize == null) {
      pageSize = 1000;
    }

    DeviceCell dc = new DeviceCell();
    dc.setSlot("01");
    dc.setPort("01");
    dc.setModuleName("Server");
    List<DeviceCell> cells = new ArrayList<>();
    cells.add(dc);

    LambdaQueryWrapper<DeviceServer> lqw = new LambdaQueryWrapper<>();
    lqw.isNotNull(DeviceServer::getDeviceSN);
    List<DeviceServer> deviceServerList = deviceServerService.list(lqw);

    List<Map<String, String>> otherList = new ArrayList<>();

    for (DeviceServer d : deviceServerList) {
      d.setCellList(cells);
      d.setOtherList(otherList);
    }

    Map<String, Object> dataResult = new HashMap<>();
    dataResult.put("dataList", deviceServerList);
    dataResult.put("pageNo", pageNo);
    dataResult.put("pageSize", pageSize);
    dataResult.put("total", deviceServerList.size());

    log.info("<[综合网管]设备查询接口>:{}", JSON.toJSONString(deviceServerList));
    return JsonResult.ok("设备列表响应成功", dataResult);
  }

  /**
   * 对某个server进行操作处理,调用该方法，将选中的数据 发送给 第三方对接系统（综合网管系统）
   *
   * @return
   */
  @PostMapping("/update/to/zhwg")
  public JsonResult update(@RequestParam String deviceId, @RequestParam String opType) {
    String nodeType = GConfig.nodeType;

    LambdaQueryWrapper<DeviceServer> lqw = new LambdaQueryWrapper<>();
    lqw.eq(DeviceServer::getModel, nodeType);
    lqw.eq(DeviceServer::getDeviceId, deviceId);
    List<DeviceServer> serverList = deviceServerService.list(lqw);
    if (serverList.size() == 0) {
      return JsonResult.fail(
          "室分服务器设备信息没有录入到数据库,请先添加数据", null, CodeEnum.ERROR_DATA_SUBMIT_NOT_EXIST_20002);
    }

    // 1表示增加，2表示删除，3表示修改
    String _opType = StringUtils.isNotBlank(opType) ? "1" : opType;
    int opTypeInt = Integer.parseInt(_opType);

    if (opTypeInt == 1) {
      log.info("<[综合网管]设备增加请求>:{}", opTypeInt);
    } else if (opTypeInt == 2) {
      log.info("<[综合网管]设备删除请求>:{}", opTypeInt);
    } else if (opTypeInt == 3) {
      log.info("<[综合网管]设备修改请求>:{}", opTypeInt);
    }

    DeviceServer deviceServer = serverList.get(0);
    DeviceCell dc = new DeviceCell();
    dc.setSlot("01");
    dc.setPort("01");
    dc.setModuleName("Server");
    List<DeviceCell> cells = new ArrayList<>();
    cells.add(dc);
    deviceServer.setCellList(cells);
    deviceServer.setOpType(opTypeInt);

    List<DeviceServer> list = new ArrayList<>();
    list.add(deviceServer);

    SFData.report("infoChangeReport", list);

    return JsonResult.ok("已向综合网管发送 室分网管服务器 [" + opType + "]请求", list);
  }
}
