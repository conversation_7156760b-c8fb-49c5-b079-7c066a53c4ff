package com.key.win.ztrs.api;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.ztrs.bean.AlarmCode;
import com.key.win.ztrs.service.IAlarmCodeService;
import com.key.win.ztrs.support.GConfig;
import com.key.win.ztrs.support.SFData;
import com.key.win.ztrs.utils.CodeEnum;
import com.key.win.ztrs.utils.JsonResult;
import com.key.win.ztrs.utils.SpringBeanUtilsExt;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.*;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/alarmCode/*")
@Api(value = "AlarmCodeAPI", tags = "告警code API")
public class AlarmCodeApi {

  @Resource private IAlarmCodeService alarmCodeService;

  /** 告警记录查询接口 */
  @GetMapping("/query")
  @ApiOperation(value = "query", tags = "[综合网管]将告警code基础数据提供给综合网管")
  public JsonResult query(
      @RequestParam(required = false) Integer pageNo,
      @RequestParam(required = false) Integer pageSize) {
    if (pageNo == null) {
      pageNo = 1;
    }
    if (pageSize == null) {
      pageSize = 1000;
    }

    List<AlarmCode> data = alarmCodeService.list();
    for (AlarmCode ac : data) {
      ac.setLine(GConfig.line);
      ac.setSystem(GConfig.clientId);
      ac.setText(ac.getDescription());
      ac.setOtherList(new ArrayList<>());
    }
    log.info("<[综合网管]告警码查询接口>:{}", JSON.toJSONString(data));
    Map<String, Object> dataResult = new HashMap<>();
    dataResult.put("dataList", data);
    dataResult.put("pageNo", pageNo);
    dataResult.put("pageSize", pageSize);
    dataResult.put("total", data.size());
    return JsonResult.ok("列表响应成功", dataResult);
  }

  @PostMapping("/changeReport")
  @ApiOperation(value = "alarmCodeChangeReport", tags = "告警code发生变化由此API上报给综合网管")
  public JsonResult alarmCodeChangeReport(@RequestBody AlarmCode ac) {
    if (ac == null) {
      return JsonResult.fail("提交数据为空", null, CodeEnum.ERROR_DATA_SUBMIT_NOT_EXIST_20002);
    }

    String line = GConfig.line;
    String system = GConfig.clientId;
    String text = ac.getText();
    ac.setDescription(text);
    String code = ac.getCode();

    Integer opType = ac.getOpType();

    if (opType == null) {
      return JsonResult.fail("opType不能为空", null, CodeEnum.ERROR_OP_ISNULL_10001);
    }
    if (opType > 3 || opType < 0) {
      return JsonResult.fail("opType类型错误", null, CodeEnum.ERROR_OP_UNKNOWN_10002);
    }

    LambdaQueryWrapper<AlarmCode> lqw = new LambdaQueryWrapper<>();
    lqw.eq(AlarmCode::getCode, code);
    AlarmCode dbAc = alarmCodeService.getOne(lqw, true);
    if (opType != 1 && dbAc == null) {
      return JsonResult.fail("操作数据不存在", null, CodeEnum.ERROR_DATA_NOT_DB_NOT_EXIST_20001);
    }

    List<AlarmCode> list = new ArrayList<>();
    // 1表示增加，2表示删除，3表示修改
    ac.setOpType(opType);
    if (opType == 1) {
      AlarmCode newAc = new AlarmCode();
      SpringBeanUtilsExt.copyProperties(ac, newAc);
      list.add(newAc);
      alarmCodeService.save(newAc);
    } else if (opType == 2) {
      alarmCodeService.removeById(dbAc.getCode());
      SpringBeanUtilsExt.copyProperties(dbAc, ac);
      ac.setLine(line);
      ac.setSystem(system);
      ac.setOpType(opType);
      ac.setText(ac.getDescription());
      list.add(ac);
    } else if (opType == 3) {
      SpringBeanUtilsExt.copyProperties(ac, dbAc);
      alarmCodeService.updateById(dbAc);
      list.add(dbAc);
    }
    SFData.report("alarmCodeChangeReport", list);
    return JsonResult.ok("已向综合网管发送[" + opType + "]请求", list);
  }
}
