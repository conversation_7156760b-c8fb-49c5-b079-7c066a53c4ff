package com.key.win.ztrs.utils;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: [zhangzhiguang] @Date: [2018-08-01 23:39] @Description: [ ] @Version: [1.0.0] @Copy:
 * [com.zzg]
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> implements Serializable {

  private static final long serialVersionUID = -4696008537295855861L;
  private T result;
  private Integer retCode;
  private String retMessage;

  public static <T> Result<T> succeed(String msg) {
    return succeedWith(null, CodeEnum.SUCCESS.getCode(), msg);
  }

  public static <T> Result<T> succeed(T model, String msg) {
    return succeedWith(model, CodeEnum.SUCCESS.getCode(), msg);
  }

  public static <T> Result<T> succeed(Integer code, String msg) {
    return succeedWith(null, code, msg);
  }

  public static <T> Result<T> succeedWith(T data, Integer code, String msg) {
    return new Result<T>(data, code, msg);
  }
}
