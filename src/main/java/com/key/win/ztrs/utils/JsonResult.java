package com.key.win.ztrs.utils;

import java.util.HashMap;

/**
 * <AUTHOR> owen
 * @version 创建时间：2017年11月12日 上午22:57:51 返回结果对象
 */
public class JsonResult extends HashMap<String, Object> {
  private static final long serialVersionUID = 1L;

  private JsonResult() {}

  /** 返回成功 */
  public static JsonResult ok(int code, String message, Object data) {
    JsonResult jsonResult = new JsonResult();
    jsonResult.put("retCode", code);
    jsonResult.put("retMessage", message);
    jsonResult.put("result", data);
    return jsonResult;
  }

  public static JsonResult ok(String message, Object data) {
    JsonResult jsonResult = new JsonResult();
    jsonResult.put("retCode", CodeEnum.SUCCESS.getCode());
    jsonResult.put("retMessage", message);
    jsonResult.put("result", data);
    return jsonResult;
  }

  public static JsonResult fail(String message, Object data, CodeEnum code) {
    JsonResult jsonResult = new JsonResult();
    jsonResult.put("retCode", code.getCode());
    jsonResult.put("retMessage", message);
    jsonResult.put("result", data);
    return jsonResult;
  }

  /** 放入object */
  @Override
  public JsonResult put(String key, Object object) {
    super.put(key, object);
    return this;
  }
}
