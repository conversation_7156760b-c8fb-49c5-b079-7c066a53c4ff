package com.key.win.ztrs.utils;

/**
 * @Author: [gitgeek] @Date: [2018-08-02 08:50] @Description: [ ] @Version: [1.0.0] @Copy: [com.zzg]
 */
public enum CodeEnum {
  SUCCESS(0),
  ERROR_990010015(990010015), // 验签失败
  ERROR_990010001(990010001), // 请求参数异常

  ERROR_DATA_NOT_DB_NOT_EXIST_20001(20001), // 数据不存在
  ERROR_DATA_SUBMIT_NOT_EXIST_20002(20002), // 数据不存在

  ERROR_DATA_OPERATION_CODE_NOT_EXIST_400001(40001), // 操作码不存在
  ERROR_DATA_OPERATION_CODE_UNKNOWN_400002(40002), // 操作碼不正確

  ERROR_OP_ISNULL_10001(10001), // Optype参数异常
  ERROR_OP_UNKNOWN_10002(10002); // Optype参数异常

  private Integer code;

  CodeEnum(Integer code) {
    this.code = code;
  }

  public Integer getCode() {
    return code;
  }

  public void setCode(Integer code) {
    this.code = code;
  }
}
