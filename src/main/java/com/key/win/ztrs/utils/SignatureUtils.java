package com.key.win.ztrs.utils;

import cn.hutool.crypto.digest.MD5;
import com.key.win.ztrs.support.GConfig;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
public class SignatureUtils {

  static Map<String, String> securityMap = new HashMap<String, String>();

  static {
    securityMap.put("clientId", GConfig.clientId);
//    securityMap.put("line", GConfig.line);
    securityMap.put("nonce", UUID.randomUUID().toString());
    securityMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
  }

  private static Map<String, String> orderedMap(Map<String, String> requestParamsMap) {
    Map<String, String> copiedMap = new TreeMap<>(securityMap);
    copiedMap.putAll(requestParamsMap);
    return copiedMap;
  }

  private static String generateSignature(Map<String, String> signatureMap) {
    StringBuilder sb = new StringBuilder();
    for (Map.Entry<String, String> entry : signatureMap.entrySet()) {
      sb.append(entry.getKey()).append("=").append(entry.getValue());
    }
    return MD5.create().digestHex(sb.toString());
  }

  private static String generateUrl(String url, Map<String, String> signatureMap) {
    // 基础URL
    boolean flag = StringUtils.endsWithIgnoreCase(url, "?");
    if (!flag) {
      url += "?";
    }
    StringBuilder urlBuilder = new StringBuilder(url);
    // 遍历Map并拼接查询参数
    for (Map.Entry<String, String> entry : signatureMap.entrySet()) {
      urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
    }

    String ztrs_url = urlBuilder.toString().substring(0, urlBuilder.length() - 1);
    return ztrs_url;
  }

  public static String buildSignatureUrl(String url, Map<String, String> requestParamsMap) {
    if (requestParamsMap == null) {
      requestParamsMap = new HashMap<>();
    }
    Map<String, String> securityMap = orderedMap(requestParamsMap);
    String signature = generateSignature(securityMap);
    securityMap.put("signature", signature);
    String finalUrl = SignatureUtils.generateUrl(url, securityMap);
    if(url.indexOf("/heartbeat/update")<=-1){
      log.info("签名请求地址:{}",finalUrl);
    }
    return finalUrl;
  }

  public static void main(String[] args) {
    String baseUrl = GConfig.url + "/heartbeat/update";
    Map<String, String> requestParamsMap = new HashMap<>();
    requestParamsMap.put("AC", "ddd");
    requestParamsMap.put("zl", "sss");
    requestParamsMap.put("ac", "ddd");
    String requestUrl = buildSignatureUrl(baseUrl, requestParamsMap);
    System.out.println(requestUrl);
  }
}
