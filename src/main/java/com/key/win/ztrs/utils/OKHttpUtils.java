package com.key.win.ztrs.utils;

import com.alibaba.fastjson2.JSON;
import java.io.IOException;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

@Slf4j
public class OKHttpUtils {

  public static void send(String requestUrl, Map bodyMap) {
    OkHttpClient client = new OkHttpClient();
    MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
    String jsonString = JSON.toJSONString(bodyMap);
    okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, jsonString);
    Request request = new Request.Builder().url(requestUrl).post(body).build();

    try {
      Response response = client.newCall(request).execute();
      if (response.isSuccessful()) {
        log.info("Response: {} ",response.body().string());
      } else {
        log.info("Request failed: {}", response.code());
      }
    } catch (IOException e) {
      throw new RuntimeException(e);
    } finally {
      client.dispatcher().executorService().shutdown();
    }
  }
}
