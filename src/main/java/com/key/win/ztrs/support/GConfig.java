package com.key.win.ztrs.support;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class GConfig implements InitializingBean {

  @Value("${api.url}")
  private String u;

  @Value("${api.clientId}")
  private String c;

  @Value("${api.line}")
  private String l;

  @Value("${api.stationCode}")
  private String s;




  /** 综合网管的访问基址 */
  public static String url = "http://10.65.90.3:8086/ims";

  /** 客户端ID */

  public static String clientId = "10";

  /** 线路ID */
  public static String line = "400";


  public static String stationCode = "1";

  @Override
  public void afterPropertiesSet() throws Exception {
    url = u;
    clientId = c;
    line = l;
    stationCode = s;
  }


}
