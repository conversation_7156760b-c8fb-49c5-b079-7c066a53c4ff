package com.key.win.ztrs.support;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.key.win.ztrs.bean.DeviceServer;
import com.key.win.ztrs.service.IDeviceServerService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GConfig implements InitializingBean {

  @Value("${api.url}")
  private String u;

  @Value("${api.clientId}")
  private String c;

  @Value("${api.line}")
  private String l;

  @Value("${api.nodeType}")
  private String n;

  /** 综合网管的访问基址 */
  public static String url = "http://10.65.90.3:8086/ims";

  /** 客户端ID */
  public static String clientId = "unknown";

  /** 线路ID */
  public static String line = "unknown";

  /** 节点主备类型 : master slaver */
  public static String nodeType = "unknown";

  public static String NODE_TYPE_MASTER = "master";
  public static String NODE_TYPE_SLAVER = "slaver";

  public static DeviceServer deviceServer;

  @Autowired private IDeviceServerService deviceServerService;

  @Override
  public void afterPropertiesSet() throws Exception {
    url = u;
    clientId = c;
    line = l;
    nodeType = n;

    LambdaQueryWrapper<DeviceServer> lqw = new LambdaQueryWrapper<>();
    lqw.eq(DeviceServer::getModel, nodeType);
    lqw.eq(DeviceServer::getLine, line);
    List<DeviceServer> list = deviceServerService.list(lqw);
    if (list.size() != 1) {
      deviceServer = null;
    }
    deviceServer = list.get(0);
  }
}
