package com.key.win.ztrs.support;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.key.win.ztrs.utils.OKHttpUtils;
import com.key.win.ztrs.utils.SignatureUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SFData {

  private static String deviceType = "4号线监控主机";

  private static Map<String, String> stationMap = new HashMap<>();

  static {
    initStationMap();
  }

  private static void initStationMap() {
    stationMap.put("1", "软三东站");
    stationMap.put("2", "后溪站");
    stationMap.put("3", "厦门北站");
    stationMap.put("4", "官浔站");
    stationMap.put("5", "丙洲岛站");
    stationMap.put("6", "城场站");
    stationMap.put("7", "双过村站");
    stationMap.put("8", "洪坑站");
    stationMap.put("9", "彭厝北站");
    stationMap.put("10", "蔡厝站");
    stationMap.put("11", "机场西站");
    stationMap.put("12", "翔安机场站");
    stationMap.put("100", "主用控制中心");
    stationMap.put("101", "备用控制中心");
    stationMap.put("102", "车辆段");
  }

  public static Map<String, String> getStations() {
    return stationMap;
  }

  public static void report(String reportType, List list) {
    String requestBaseUrl = "";
    if (reportType.equals("infoChangeReport")) {
      requestBaseUrl = GConfig.url + "/device/update";
    } else if (reportType.equals("statusChangeReport")) {
      requestBaseUrl = GConfig.url + "/device/status/update";
    } else if (reportType.equals("alarmCodeChangeReport")) {
      requestBaseUrl = GConfig.url + "/alarmCode/update";
    } else if(reportType.equals("alarmReport")) {
      requestBaseUrl = GConfig.url + "/alarm/update";
    }else if(reportType.equals("operationReport")) {
      requestBaseUrl = GConfig.url + "/operation/update";
    }

    Map<String, String> requestParamsMap = new HashMap<>();
    String requestUrl = SignatureUtils.buildSignatureUrl(requestBaseUrl, requestParamsMap);

    Map<String, Object> bodyMap = new HashMap<>();
    bodyMap.put("time", DateTime.now().toString(DatePattern.NORM_DATETIME_MS_PATTERN));
    bodyMap.put("rows", list.size());
    bodyMap.put("dataList", list);
    OKHttpUtils.send(requestUrl, bodyMap);
  }

  public static void reportMap(String reportType, Map<String,Object> reportMap) {
    String requestBaseUrl = "";
     if(reportType.equals("alarmOperationChangeReport")){
      requestBaseUrl = GConfig.url + "/alarm/operation";
    }

    Map<String, String> requestParamsMap = new HashMap<>();
    String requestUrl = SignatureUtils.buildSignatureUrl(requestBaseUrl, requestParamsMap);

    OKHttpUtils.send(requestUrl, reportMap);
  }

  public static String operationCode2Desc(String operationCode) {
    Map<String, String> codeMap = new HashMap<>();
    codeMap.put("1", "手动确认");
    codeMap.put("2", "手动恢复");
    codeMap.put("3", "手动反确认");
    return codeMap.get(operationCode);
  }
}
