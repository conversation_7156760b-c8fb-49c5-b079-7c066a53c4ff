package com.key.win.ztrs.support;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import com.key.win.ztrs.utils.OKHttpUtils;
import com.key.win.ztrs.utils.SignatureUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SFData {

  private static String deviceType = "4号线监控主机";

  private static Map<String, String> line4stationMap = new HashMap<>();
  private static Map<String, String> line6stationMap = new HashMap<>();
  private static Map<String, Map<String, String>> stationMap = new HashMap<>();

  static {
    initLine4StationMap();
    initLine6StationMap();
    stationMap.put("400", line4stationMap);
    stationMap.put("600", line6stationMap);
  }

  private static void initLine4StationMap() {
    line4stationMap.put("1", "集美软件园北站");
    line4stationMap.put("2", "后溪站");
    line4stationMap.put("3", "厦门北站");
    line4stationMap.put("4", "官浔站");
    line4stationMap.put("5", "丙洲站");
    line4stationMap.put("6", "城场站");
    line4stationMap.put("7", "西岩站");
    line4stationMap.put("8", "洪坑站");
    line4stationMap.put("9", "前浯站");
    line4stationMap.put("10", "洞庭站");
    line4stationMap.put("11", "机场西站");
    line4stationMap.put("12", "翔安机场站");
    line4stationMap.put("100", "主用控制中心");
    line4stationMap.put("101", "备用控制中心");
    line4stationMap.put("102", "车辆段");
  }

  private static void initLine6StationMap() {
    line6stationMap.put("1", "龙江明珠站");
    line6stationMap.put("2", "石美站");
    line6stationMap.put("3", "角江路站");
    line6stationMap.put("4", "角美中心站");
    line6stationMap.put("5", "文圃路站");
    line6stationMap.put("6", "角海路站");
    line6stationMap.put("7", "角美东站");
    line6stationMap.put("8", "一农站");
    line6stationMap.put("9", "林埭站");
    line6stationMap.put("10", "鼎美站");
    line6stationMap.put("11", "马銮中心站");
    line6stationMap.put("12", "陈井站");
    line6stationMap.put("13", "西滨站");
    line6stationMap.put("14", "碑头站");
    line6stationMap.put("15", "杏滨站");
    line6stationMap.put("16", "内茂站");
    line6stationMap.put("17", "董任站");
    line6stationMap.put("18", "官任站");
    line6stationMap.put("19", "杏林湾站");
    line6stationMap.put("20", "华侨大学站");
    line6stationMap.put("100", "主用控制中心");
    line6stationMap.put("101", "备用控制中心");
    line6stationMap.put("102", "车辆段");
  }

  public static Map<String, String> getStations(String lineId) {
    return stationMap.get(lineId);
  }

  public static void report(String reportType, List list) {
    String requestBaseUrl = "";
    if (reportType.equals("infoChangeReport")) {
      requestBaseUrl = GConfig.url + "/device/update";
    } else if (reportType.equals("statusChangeReport")) {
      requestBaseUrl = GConfig.url + "/device/status/update";
    } else if (reportType.equals("alarmCodeChangeReport")) {
      requestBaseUrl = GConfig.url + "/alarmCode/update";
    } else if (reportType.equals("alarmReport")) {
      requestBaseUrl = GConfig.url + "/alarm/update";
    } else if (reportType.equals("operationReport")) {
      requestBaseUrl = GConfig.url + "/operation/update";
    }

    Map<String, String> requestParamsMap = new HashMap<>();
    String requestUrl = SignatureUtils.buildSignatureUrl(requestBaseUrl, requestParamsMap);

    Map<String, Object> bodyMap = new HashMap<>();
    bodyMap.put("time", DateTime.now().toString(DatePattern.NORM_DATETIME_MS_PATTERN));
    bodyMap.put("rows", list.size());
    bodyMap.put("dataList", list);
    OKHttpUtils.send(requestUrl, bodyMap);
  }

  public static void reportMap(String reportType, Map<String, Object> reportMap) {
    String requestBaseUrl = "";
    if (reportType.equals("alarmOperationChangeReport")) {
      requestBaseUrl = GConfig.url + "/alarm/operation";
    }

    Map<String, String> requestParamsMap = new HashMap<>();
    String requestUrl = SignatureUtils.buildSignatureUrl(requestBaseUrl, requestParamsMap);

    OKHttpUtils.send(requestUrl, reportMap);
  }

  public static String operationCode2Desc(String operationCode) {
    Map<String, String> codeMap = new HashMap<>();
    codeMap.put("1", "手动确认");
    codeMap.put("2", "手动恢复");
    codeMap.put("3", "手动反确认");
    return codeMap.get(operationCode);
  }
}
